Stack trace:
Frame         Function      Args
0007FFFFB740  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFB740, 0007FFFFA640) msys-2.0.dll+0x1FEBA
0007FFFFB740  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBA18) msys-2.0.dll+0x67F9
0007FFFFB740  000210046832 (000210285FF9, 0007FFFFB5F8, 0007FFFFB740, 000000000000) msys-2.0.dll+0x6832
0007FFFFB740  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFB740  0002100690B4 (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBA20  00021006A49D (0007FFFFB750, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFD9CA80000 ntdll.dll
7FFD9B7E0000 KERNEL32.DLL
7FFD9A120000 KERNELBASE.dll
7FFD9A9B0000 USER32.dll
7FFD9A710000 win32u.dll
7FFD9C530000 GDI32.dll
7FFD9A7B0000 gdi32full.dll
7FFD99F10000 msvcp_win.dll
7FFD9A570000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFD9C560000 advapi32.dll
7FFD9B730000 msvcrt.dll
7FFD9C070000 sechost.dll
7FFD9C2D0000 RPCRT4.dll
7FFD995D0000 CRYPTBASE.DLL
7FFD9A690000 bcryptPrimitives.dll
7FFD9C4F0000 IMM32.DLL
