export const translations = {
  ar: {
    // Language Selection
    selectLanguage: "اختر اللغة",
    arabic: "العربية",
    french: "الفرنسية",
    english: "الإنجليزية",
    languageSelected: "تم اختيار اللغة بنجاح",

    // Login & Authentication
    welcome: "مرحباً بك",
    loginPrompt: "قم بتسجيل الدخول للوصول إلى حسابك",
    username: "اسم المستخدم",
    password: "كلمة المرور",
    rememberMe: "تذكرني",
    login: "تسجيل الدخول",
    logout: "خروج",
    systemName: "نظام المحاسبي",
    allRightsReserved: "جميع الحقوق محفوظة",
    version: "الإصدار",
    systemDescription: "النظام المحاسبي المتكامل لإدارة أعمالك",
    feature1: "إدارة المبيعات والمشتريات بكفاءة عالية",
    feature2: "إدارة المخزون ومراقبة الأصناف",
    feature3: "التقارير المالية والمحاسبية المتكاملة",
    feature4: "إدارة العملاء والموردين بسهولة",

    // User Management
    systemUser: "مستخدم النظام",
    systemManager: "مدير النظام",
    seller: "بائع",
    manager: "المدير",

    // Navigation
    dashboard: "لوحة التحكم",
    products: "المنتجات",
    sales: "المبيعات",
    customers: "الزبائن",
    purchases: "المشتريات",
    reports: "التقارير",
    settings: "الإعدادات",
    users: "المستخدمين",

    // Dashboard
    systemOverview: "نظرة عامة على أداء النظام",
    totalSales: "إجمالي المبيعات",
    totalInvoices: "عدد الفواتير",
    totalProducts: "إجمالي المنتجات",
    lowStockProducts: "منتجات منخفضة المخزون",
    recentInvoicesOperations: "آخر الفواتير والعمليات",
    quickOperations: "العمليات السريعة",
    chooseOperation: "اختر العملية التي تريد تنفيذها",

    // Dashboard Stats
    invoice: "فاتورة",
    invoices: "فاتورة",
    product: "منتج",
    products: "منتج",

    // Table Headers
    invoiceNumber: "رقم الفاتورة",
    date: "التاريخ",
    supplier: "المورد",
    customer: "العميل",
    amountPaid: "المبلغ المدفوع",
    totalAmount: "المبلغ الإجمالي",
    status: "الحالة",

    // Recent Operations
    recentInvoicesAndOperations: "آخر الفواتير والعمليات",

    // Status
    paid: "مدفوعة",
    unpaid: "غير مدفوعة",
    debt: "دين",
    active: "نشط",
    inactive: "غير نشط",

    // Customer Names
    walkInCustomer: "زبون عابر",

    // Quick Actions
    quickSalesInvoice: "فاتورة مبيعات سريعة",
    quickPurchaseInvoice: "فاتورة مشتريات سريعة",
    quickAddProduct: "إضافة منتج سريع",
    newSalesInvoice: "فاتورة مبيعات جديدة",
    newPurchaseInvoice: "فاتورة مشتريات جديدة",
    purchaseReport: "تقرير المشتريات",
    purchaseStatistics: "إحصائيات المشتريات",
    salesManagement: "إدارة المبيعات",
    inventoryManagement: "إدارة المخزون",
    customerManagement: "إدارة الزبائن",

    // Dashboard KPIs and Performance
    performanceOverview: "نظرة عامة على الأداء",
    netProfit: "صافي الربح",
    profitMargin: "هامش الربح",
    todaySales: "مبيعات اليوم",
    monthlySales: "مبيعات الشهر",
    monthlyGrowth: "نمو شهري",
    allPeriods: "جميع الفترات",
    growth: "نمو",
    margin: "هامش",

    // Units and Currency
    dinar: "دينار",
    dinars: "دينار",
    unit: "وحدة",
    units: "وحدة",

    // Sales
    newSalesInvoice: "فاتورة مبيعات جديدة",
    newInvoice: "فاتورة جديدة",
    report: "تقرير",
    totalSales: "إجمالي المبيعات",
    invoiceCount: "عدد الفواتير",
    averageInvoice: "متوسط الفاتورة",
    creditInvoices: "فواتير دين",
    invoiceNumber: "رقم الفاتورة",
    date: "التاريخ",
    customer: "الزبون",
    paymentMethod: "طريقة الدفع",
    amount: "المبلغ",
    status: "الحالة",
    actions: "الإجراءات",
    noInvoicesSaved: "لا توجد فواتير محفوظة",
    walkInCustomer: "زبون عابر",
    cash: "نقداً",
    credit: "دين",
    paid: "مدفوعة",
    debt: "دين",
    viewInvoiceDetails: "عرض تفاصيل الفاتورة",
    normalPrint: "طباعة عادية",
    thermalPrint: "طباعة حرارية",
    editInvoice: "تعديل الفاتورة (المدير فقط)",
    returnProducts: "إرجاع منتجات",
    deleteInvoice: "حذف الفاتورة (المدير فقط)",
    scanBarcode: "مسح الباركود",
    product: "المنتج",
    quantity: "الكمية",
    price: "السعر",
    total: "المجموع",
    add: "إضافة",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    discount: "الخصم",
    finalTotal: "المجموع النهائي",
    saveInvoice: "حفظ الفاتورة",
    cancel: "إلغاء",
    delete: "حذف",
    view: "عرض",
    print: "طباعة",

    // Sales Report
    salesReport: "تقرير المبيعات",
    reportDate: "تاريخ التقرير",
    generationTime: "وقت الإنشاء",
    salesSummary: "ملخص المبيعات",
    cashSales: "المبيعات النقدية",
    salesInvoiceDetails: "تفاصيل فواتير المبيعات",
    noSalesInvoicesToDisplay: "لا توجد فواتير مبيعات لعرضها",
    additionalStatistics: "إحصائيات إضافية",
    totalTaxes: "إجمالي الضرائب",
    totalDiscounts: "إجمالي الخصومات",
    creditValue: "قيمة الديون",
    accountingSystemIntegratedBusinessManagement: "نظام المحاسبي - إدارة الأعمال المتكاملة",
    reportGeneratedAutomatically: "تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ",
    allRightsReserved: "جميع الحقوق محفوظة",
    finalAmount: "المبلغ النهائي",
    salesReportOpened: "تم فتح تقرير المبيعات الاحترافي للطباعة",

    // Sales Invoice Modal
    saveInvoiceShortcut: "حفظ الفاتورة",
    addProductShortcut: "إضافة منتج",
    closeWithoutSaving: "إغلاق بدون حفظ",
    searchProductPlaceholder: "ابحث عن منتج (الاسم، الرمز، الباركود)...",
    selectProduct: "اختر منتج",
    available: "متوفر",
    invoiceItems: "عناصر الفاتورة",
    noProductsAdded: "لم يتم إضافة أي منتجات بعد",
    useBarcodeOrSelect: "استخدم الباركود أو اختر من القائمة",
    productName: "المنتج",
    quantityRequiredExceedsStock: "الكمية المطلوبة أكبر من المتوفر",
    deleteTitle: "حذف",
    selectRegisteredCustomer: "اختر زبون مسجل",
    action: "إجراء",
    activeBarcodeReader: "قارئ باركود نشط",

    // Purchase Management
    purchaseManagement: "إدارة المشتريات",
    newPurchaseInvoice: "فاتورة مشتريات جديدة",
    totalPurchases: "إجمالي المشتريات",
    purchaseInvoiceCount: "عدد فواتير المشتريات",
    averagePurchaseInvoice: "متوسط فاتورة المشتريات",
    creditPurchaseInvoices: "فواتير دين",
    supplier: "المورد",
    noPurchaseInvoicesSaved: "لا توجد فواتير مشتريات محفوظة",
    generalSupplier: "مورد عام",
    editPurchaseInvoice: "تعديل فاتورة المشتريات (المدير فقط)",
    deletePurchaseInvoice: "حذف فاتورة المشتريات (المدير فقط)",

    // Purchase Invoice Modal
    editPurchaseInvoiceTitle: "تعديل فاتورة المشتريات",
    savePurchaseInvoice: "حفظ فاتورة المشتريات",
    updatePurchaseInvoice: "تحديث فاتورة المشتريات",
    purchaseInvoiceItems: "عناصر فاتورة المشتريات",
    noPurchaseProductsAdded: "لم يتم إضافة أي منتجات بعد",
    selectProductsFromList: "اختر المنتجات من القائمة أعلاه",
    selectRegisteredSupplier: "اختر مورد مسجل",
    addNewSupplier: "+ إضافة مورد جديد",
    purchasePrice: "سعر الشراء",
    addProductShortcut: "إضافة منتج",
    closeWithoutSaving: "إغلاق بدون حفظ",
    selectProduct: "اختر منتج",
    available: "متوفر",
    addNewProduct: "+ إضافة منتج جديد",
    quantity: "الكمية",
    add: "إضافة",
    productName: "المنتج",
    total: "المجموع",
    action: "إجراء",
    delete: "حذف",
    discount: "الخصم",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    finalTotal: "المجموع النهائي",
    cancel: "إلغاء",

    // Customers
    customersManagement: "إدارة الزبائن",
    addNewCustomer: "إضافة زبون جديد",
    customerName: "اسم الزبون",
    phone: "الهاتف",
    email: "البريد الإلكتروني",
    address: "العنوان",
    company: "الشركة",
    balance: "الرصيد",
    creditLimit: "حد الائتمان",
    paymentTerm: "فترة السداد",
    status: "الحالة",
    active: "نشط",
    inactive: "غير نشط",
    actions: "الإجراءات",

    // Customer Management Page
    totalCustomers: "إجمالي الزبائن",
    totalDues: "إجمالي المستحقات",
    debtorCustomers: "زبائن مدينين",
    customerNumber: "رقم الزبون",
    profitMarginDiscount: "خصم (%)",
    editCustomer: "تعديل الزبون",
    deleteCustomer: "حذف الزبون",
    searchCustomers: "البحث في الزبائن (الاسم، الهاتف، البريد الإلكتروني)...",
    searchSalesInvoices: "البحث في فواتير المبيعات (رقم الفاتورة، اسم الزبون)...",
    searchPurchaseInvoices: "البحث في فواتير المشتريات (رقم الفاتورة، اسم المورد)...",
    noCustomersMatchingFilter: "لا توجد زبائن مطابقين للفلتر",
    noCustomersAdded: "لا توجد زبائن مضافين",
    noInvoicesMatchingFilter: "لا توجد فواتير مطابقة للفلتر",
    noPurchaseInvoicesMatchingFilter: "لا توجد فواتير مشتريات مطابقة للفلتر",
    addNewCustomerTitle: "إضافة عميل جديد",
    customerID: "رقم العميل",
    companyName: "اسم الشركة",
    phoneNumber: "رقم الهاتف",
    emailAddress: "البريد الإلكتروني",
    fullAddress: "العنوان الكامل",
    openingBalance: "الرصيد الافتتاحي",
    creditLimitAmount: "حد الائتمان",
    paymentTermDays: "فترة السداد (بالأيام)",
    save: "حفظ",
    cancel: "إلغاء",
    customerSavedSuccess: "تم حفظ بيانات العميل بنجاح",
    discountAppliedToProfit: "يتم تطبيق الخصم على هامش الربح وليس إجمالي المبيعات",

    // Customer Actions
    printCustomerData: "طباعة بيانات الزبون",
    thermalPrintCustomer: "طباعة حرارية للزبون",
    viewCustomerOperations: "عرض عمليات الزبون",
    confirmDeleteCustomer: "هل أنت متأكد من حذف هذا الزبون؟",
    yes: "نعم",
    no: "لا",
    customerDeletedSuccessfully: "تم حذف الزبون بنجاح",
    customerOperationsTitle: "عمليات الزبون",
    noOperationsFound: "لا توجد عمليات لهذا الزبون",
    customerReport: "تقرير الزبون",

    // Payment functionality
    payCustomerDebt: "تسديد فواتير الزبون",
    paymentDetails: "تفاصيل الدفع",
    paymentAmount: "مبلغ الدفع",
    maxPaymentAmount: "الحد الأقصى",
    currentDebt: "الدين الحالي",
    remainingBalance: "الرصيد المتبقي",
    processPayment: "تأكيد الدفع",
    paymentProcessedSuccessfully: "تم تسديد المبلغ بنجاح",
    pleaseEnterValidAmount: "يرجى إدخال مبلغ صحيح",
    paymentExceedsBalance: "المبلغ المدخل أكبر من رصيد الزبون",
    for: "لـ",

    // Customer Operations with Payment
    customerOperationsWithPayment: "عمليات ومدفوعات الزبون",
    paymentHistory: "تاريخ المدفوعات",
    makePayment: "إجراء دفعة",
    paymentDate: "تاريخ الدفع",
    paymentReference: "مرجع الدفع",
    noPaymentsFound: "لا توجد مدفوعات لهذا الزبون",
    totalPaid: "إجمالي المدفوع",
    outstandingBalance: "الرصيد المستحق",
    lastPayment: "آخر دفعة",
    paymentSuccessful: "تم الدفع بنجاح",
    enterPaymentAmount: "أدخل مبلغ الدفع",
    paymentNotes: "ملاحظات الدفع",

    // Print transactions
    customerTransactionsReport: "تقرير معاملات الزبون",
    transactionsAnalysis: "تحليل المعاملات",
    transactionsSummary: "ملخص المعاملات",
    totalTransactions: "إجمالي المعاملات",
    cashTransactions: "المعاملات النقدية",
    creditTransactions: "معاملات الدين",
    transactionsList: "قائمة المعاملات",
    transactionsReportOpened: "تم فتح تقرير المعاملات للطباعة",
    thermalTransactionsReportOpened: "تم فتح تقرير المعاملات للطباعة الحرارية",
    printA4: "طباعة A4",
    printThermal: "طباعة حرارية",
    creditOperations: "عمليات الدين",

    // Products & Inventory Management
    productsManagement: "إدارة المنتجات",
    inventoryManagement: "إدارة المخزون",
    addNewProduct: "إضافة منتج جديد",
    newProduct: "منتج جديد",
    productName: "اسم المنتج",
    category: "الفئة",
    barcode: "الباركود",
    stock: "المخزون",
    minStock: "الحد الأدنى للمخزون",

    // Inventory Page Header
    dataManagement: "البيانات",
    exportExcel: "تصدير Excel",
    jsonBackup: "نسخة احتياطية JSON",
    importData: "استيراد بيانات",
    deleteAllData: "حذف جميع البيانات",
    print: "طباعة",

    // Search and Filters
    searchProducts: "البحث في المنتجات (الاسم، الرمز، الباركود)...",
    allCategories: "جميع الفئات",
    allStatuses: "جميع الحالات",
    manageCategories: "إدارة الفئات",
    clearFilters: "مسح الفلاتر",

    // Table Headers
    productCode: "رمز المنتج",
    productName: "اسم المنتج",
    barcode: "الباركود",
    category: "الفئة",
    buyPrice: "سعر الشراء",
    sellPrice: "سعر البيع",
    availableQuantity: "الكمية المتوفرة",
    minStock: "الحد الأدنى",
    totalValue: "القيمة الإجمالية",
    status: "الحالة",
    actions: "الإجراءات",

    // Product Status
    normal: "عادي",
    high: "مرتفع",
    low: "منخفض",
    outOfStock: "نفد",
    notSpecified: "غير محدد",

    // Table Messages
    noProductsFound: "لا توجد منتجات تطابق معايير البحث",
    noProductsInInventory: "لا توجد منتجات في المخزون",
    clickToEdit: "انقر للتعديل",
    currentBarcode: "الباركود الحالي",
    shiftEnterSaveAndPrint: "Shift+Enter: تم حفظ الفاتورة وإرسالها للطباعة الحرارية",
    enterSaveOnly: "Enter: تم حفظ الفاتورة",
    addProductByBarcode: "إضافة منتج بالباركود",
    scanBarcodeToAddProduct: "امسح الباركود لإضافة منتج للفاتورة",
    addProductFromList: "إضافة منتج من القائمة",
    selectProductOption: "اختر منتج",

    // Summary Cards
    displayedProducts: "المنتجات المعروضة",
    totalProducts: "إجمالي المنتجات",
    totalValue: "القيمة الإجمالية",
    lowStockProducts: "منتجات منخفضة المخزون",
    outOfStockProducts: "منتجات نفدت",
    outOfTotal: "من أصل",

    // Product Actions
    editProduct: "تعديل (المدير فقط)",
    deleteProduct: "حذف (المدير فقط)",
    requestStockUpdate: "طلب تعديل الكمية (يحتاج موافقة المدير)",
    managerOnly: "المدير فقط",

    // Bulk Selection
    selectAll: "تحديد الكل",
    selected: "محدد",
    deleteSelected: "حذف المحدد",
    pleaseSelectItems: "يرجى تحديد عناصر للحذف",
    confirmDeleteSelected: "هل أنت متأكد من حذف العناصر المحددة؟",
    selectedItemsDeleted: "تم حذف العناصر المحددة",
    andStockRestored: "وإرجاع المنتجات للمخزون",
    andStockAdjusted: "وتعديل المخزون",
    stockWillBeAdjusted: "سيتم تعديل المخزون تلقائياً",
    allProductsWillBeRestored: "سيتم إرجاع جميع المنتجات إلى المخزون",
    notAllowedManagerOnlyBulkDelete: "غير مسموح - المدير فقط يمكنه الحذف المتعدد",

    // Product Modal
    editExistingProduct: "تعديل منتج موجود",
    addNewProduct: "إضافة منتج جديد",
    scanBarcode: "مسح الباركود",
    scanBarcodeOrEnter: "امسح الباركود أو أدخله يدوياً - سيتم التوليد التلقائي إذا تُرك فارغاً",
    generateAutoBarcode: "توليد باركود تلقائي",
    clearBarcode: "مسح الباركود",
    barcodeHelp: "يمكنك مسح الباركود باستخدام قارئ الباركود أو إدخاله يدوياً. إذا كان الباركود موجود في المخزون، سيتم عرض معلومات المنتج للتعديل. إذا تُرك فارغاً، سيتم توليد باركود تلقائياً عند الحفظ.",
    productNumber: "رقم المنتج",
    enterProductName: "أدخل اسم المنتج",
    selectCategory: "اختر الفئة",
    currentBarcode: "📷 الباركود الحالي",
    barcodeWillShow: "سيتم عرض الباركود هنا",
    buyPrice: "سعر الشراء",
    sellPrice: "سعر البيع",
    currentQuantity: "الكمية الحالية",
    minStock: "الحد الأدنى",
    saveChanges: "حفظ التعديلات",
    saveProduct: "حفظ المنتج",
    cancel: "إلغاء",

    // Category Management Modal
    categoryManagement: "إدارة فئات المنتجات",
    addNewCategory: "إضافة فئة جديدة",
    newCategoryName: "اسم الفئة الجديدة",
    add: "إضافة",
    existingCategories: "الفئات الموجودة",
    edit: "تعديل",
    delete: "حذف",

    // Settings
    settings: "الإعدادات",
    storeAndSellersManagement: "إدارة إعدادات المتجر والبائعين",
    storeSettings: "إعدادات المتجر",
    editSettings: "تعديل الإعدادات",
    storeName: "اسم المتجر",
    phoneNumber: "رقم الهاتف",
    storePhone: "هاتف المتجر",
    storeAddress: "عنوان المتجر",
    address: "العنوان",
    currency: "العملة",
    taxRate: "معدل الضريبة",
    language: "اللغة",
    theme: "المظهر",
    lightMode: "الوضع الفاتح",
    darkMode: "الوضع المظلم",
    save: "حفظ",
    saveSettings: "حفظ الإعدادات",

    // Seller Management
    sellersManagement: "إدارة البائعين",
    addNewSeller: "إضافة بائع جديد",
    sellerNumber: "الرقم",
    sellerName: "الاسم",
    username: "اسم المستخدم",
    phone: "الهاتف",
    role: "الدور",
    status: "الحالة",
    creationDate: "تاريخ الإنشاء",
    actions: "الإجراءات",
    admin: "مدير",
    seller: "بائع",
    active: "نشط",
    inactive: "غير نشط",
    activate: "تفعيل",
    deactivate: "إيقاف",
    delete: "حذف",

    // Seller Modal
    addNewSellerTitle: "إضافة بائع جديد",
    sellerID: "رقم البائع",
    sellerNameLabel: "اسم البائع",
    usernameLabel: "اسم المستخدم",
    passwordLabel: "كلمة المرور",
    phoneLabel: "رقم الهاتف",
    emailLabel: "البريد الإلكتروني",
    roleLabel: "الدور",
    enterSellerName: "أدخل اسم البائع",
    enterUsername: "أدخل اسم المستخدم",
    enterPassword: "أدخل كلمة المرور",
    enterPhone: "أدخل رقم الهاتف",
    enterEmail: "أدخل البريد الإلكتروني",
    selectRole: "اختر الدور",

    // Store Settings Modal
    storeSettingsModal: "إعدادات المتجر",
    storeNameRequired: "اسم المتجر",
    enterStoreName: "أدخل اسم المتجر",
    storeNumberLabel: "رقم المتجر",
    storeNumberPlaceholder: "ST001",
    phoneNumberLabel: "رقم الهاتف",
    phoneNumberPlaceholder: "+*********** 456",
    addressLabel: "العنوان",
    fullStoreAddress: "العنوان الكامل للمتجر",
    taxRateLabel: "معدل الضريبة (%)",
    taxRatePlaceholder: "19",
    currencyLabel: "العملة",
    algerianDinar: "دينار جزائري (DZD)",
    usDollar: "دولار أمريكي (USD)",
    euro: "يورو (EUR)",
    adminPasscodeLabel: "كلمة مرور الإدارة",
    adminPasscodePlaceholder: "010290",
    adminPasscodeHelp: "كلمة المرور المطلوبة لحذف وتعديل البيانات الحساسة",
    storeLogo: "شعار المتجر",
    logoPreview: "شعار المتجر",

    // Edit Customer Modal
    editCustomerData: "تعديل بيانات الزبون",
    customerID: "رقم الزبون",
    customerNameRequired: "اسم الزبون",
    enterCustomerName: "أدخل اسم الزبون",
    phoneNumberPlaceholder: "رقم الهاتف",
    emailPlaceholder: "البريد الإلكتروني",
    addressPlaceholder: "العنوان",
    companyPlaceholder: "اسم الشركة",
    openingBalance: "الرصيد الافتتاحي",
    creditLimit: "حد الائتمان",
    paymentTermDays: "فترة السداد (بالأيام)",
    profitMarginDiscount: "خصم هامش الربح (%)",
    discountPercentagePlaceholder: "نسبة الخصم من هامش الربح",
    discountAppliedToProfit: "يتم تطبيق الخصم على هامش الربح وليس إجمالي المبيعات",
    saveChanges: "حفظ التغييرات",

    // Expenses Modal
    editExpense: "تعديل مصروف",
    addNewExpense: "إضافة مصروف جديد",
    date: "التاريخ",
    category: "الفئة",
    amount: "المبلغ",
    paymentMethod: "طريقة الدفع",
    description: "الوصف",
    expenseDescription: "أدخل وصفاً تفصيلياً للمصروف",
    salariesWages: "رواتب وأجور",
    rent: "إيجار",
    utilities: "مرافق (كهرباء، ماء، إنترنت)",
    taxesFees: "ضرائب ورسوم",
    marketingAdvertising: "تسويق وإعلان",
    maintenanceRepairs: "صيانة وإصلاحات",
    transportationTravel: "نقل ومواصلات",
    otherExpenses: "مصاريف أخرى",

    // Repair Management Translations
    repairManagement: "إدارة الإصلاحات",
    repairs: "الإصلاحات",
    newBonPour: "بون بور جديد",
    repairCompleted: "إصلاح مكتمل",
    clientPickup: "استلام من العميل",
    repairOrder: "أمر إصلاح",
    createRepairOrder: "إنشاء أمر إصلاح",

    // Form Fields
    clientDeviceInfo: "معلومات العميل والجهاز",
    clientFullName: "الاسم الكامل للعميل",
    problemTypeInfo: "نوع المشكلة والوصف",
    pricingPaymentInfo: "السعر والدفع",
    dateDetailsInfo: "التاريخ والتفاصيل",
    deviceName: "اسم الجهاز",
    deviceType: "نوع الجهاز",
    problemDescription: "وصف المشكلة",
    repairPrice: "سعر الإصلاح",
    partialPayment: "دفع جزئي",
    paymentStatus: "حالة الدفع",
    depositDate: "تاريخ الإيداع",
    depositTime: "وقت الإيداع",
    repairBarcode: "باركود الإصلاح",
    remarks: "ملاحظات",

    // Device Types
    smartphone: "هاتف ذكي",
    tablet: "جهاز لوحي",
    pc: "حاسوب شخصي",
    console: "جهاز ألعاب",

    // Problem Types
    lcd: "شاشة LCD",
    lcdWithFrame: "شاشة LCD مع الإطار",
    chargingPort: "منفذ الشحن",
    glass: "زجاج",
    backGlass: "زجاج خلفي",
    battery: "بطارية",
    frontCamera: "كاميرا أمامية",
    rearCamera: "كاميرا خلفية",
    microphone: "ميكروفون",
    loudspeaker: "مكبر صوت",
    earpieceSpeaker: "سماعة الأذن",
    waterDamage: "ضرر مائي",
    powerButton: "زر التشغيل",
    volumeButtons: "أزرار الصوت",
    homeButton: "زر الرئيسية",
    slowPerformance: "أداء بطيء",
    androidCorruption: "فساد نظام أندرويد",
    osCorruption: "فساد نظام التشغيل",
    wifiIssues: "مشاكل الواي فاي",
    bluetoothIssues: "مشاكل البلوتوث",
    cellularNetworkIssues: "مشاكل الشبكة الخلوية",
    noSound: "لا يوجد صوت",
    headphoneJackIssues: "مشاكل مقبس السماعة",
    vibratorMotorFailure: "عطل محرك الاهتزاز",
    proximitySensor: "مستشعر القرب",
    gyroscope: "الجيروسكوب",
    fingerprintSensor: "مستشعر البصمة",
    overheating: "ارتفاع درجة الحرارة",
    storageFull: "امتلاء التخزين",
    backlightIssues: "مشاكل الإضاءة الخلفية",
    housingDamage: "تلف الهيكل",
    addNewProblem: "إضافة مشكلة جديدة",

    // Payment Status
    paid: "مدفوع",
    partiallyPaid: "مدفوع جزئياً",
    unpaid: "غير مدفوع",

    // Repair Status
    inProcess: "قيد المعالجة",
    waitingForClient: "في انتظار العميل",
    notSuccess: "غير ناجح",
    done: "مكتمل",

    // Actions
    createAndPrint: "إنشاء وطباعة",
    cancel: "إلغاء",
    viewInfo: "عرض المعلومات",
    edit: "تعديل",
    printQRCode: "طباعة رمز QR",
    printBonPour: "طباعة بون بور",

    // Process Messages
    repairCompletedSuccess: "تم إكمال الإصلاح بنجاح",
    repairCompletedFailure: "فشل في إكمال الإصلاح",
    partsChangedPrice: "سعر القطع المستبدلة",
    clientPickupCompleted: "تم استلام العميل بنجاح",

    // Table Headers
    clientName: "اسم العميل",
    deviceNameHeader: "اسم الجهاز",
    problem: "المشكلة",
    repairPriceHeader: "سعر الإصلاح",
    partsPrice: "سعر القطع",
    interestRate: "معدل الفائدة",
    situation: "الحالة",
    actions: "الإجراءات",

    // Admin Protection
    adminPasscodeRequired: "كلمة مرور المدير مطلوبة",
    enterAdminPasscode: "أدخل كلمة مرور المدير",
    enterAdminPasscodeToEdit: "أدخل كلمة مرور المدير لتعديل الإصلاح",
    incorrectPasscode: "كلمة مرور خاطئة",
    editRepair: "تعديل الإصلاح",
    deleteRepair: "حذف الإصلاح",
    enterPasscode: "أدخل كلمة المرور",

    // QR Scanner
    scanQRCode: "مسح رمز QR",
    qrScannerActive: "ماسح رمز QR نشط",
    qrCodeScanned: "تم مسح رمز QR",
    qrCode: "رمز QR",
    repairQRCode: "رمز QR للإصلاح",
    repairDetails: "تفاصيل الإصلاح",
    clickToEnlarge: "انقر للتكبير",
    qrGenerationError: "خطأ في إنشاء رمز QR",
    printTicket: "طباعة التذكرة",
    totalPrice: "السعر الإجمالي",
    repairCompletionQuestion: "هل نجح الإصلاح؟",
    didRepairSucceed: "هل نجح الإصلاح؟",
    repairSuccess: "نجح الإصلاح",
    repairNotSuccess: "لم ينجح الإصلاح",
    supplierName: "اسم المورد",
    selectSupplier: "اختر المورد",
    printingQRCode: "طباعة رمز QR",
    qrCodePrinted: "تم طباعة رمز QR",
    printingError: "خطأ في الطباعة",
    scanQRForRecovery: "مسح رمز QR للمعلومات التلقائية للعميل",
    scanForAutoFill: "امسح لملء معلومات العميل والهاتف تلقائياً",
    manualSearchPlaceholder: "أو ابحث يدوياً باسم العميل...",
    findRepairToRecover: "العثور على الإصلاح للاستلام",
    scanOrSelectRepair: "امسح رمز QR أو اختر يدوياً",
    qrCodeScanner: "ماسح رمز QR",
    quickestMethod: "الطريقة الأسرع",
    manualSearch: "البحث اليدوي",
    searchByName: "البحث بالاسم",
    selectFromList: "اختر من القائمة",
    browseAllReady: "تصفح جميع الإصلاحات الجاهزة",
    scanCodeBarre: "مسح الباركود",
    clientInformation: "معلومات العميل",
    phoneNumber: "رقم الهاتف",
    priceBreakdown: "تفصيل الأسعار",
    saveAndComplete: "حفظ وإكمال",
    optionalPrintActions: "اختياري: اضغط Shift + Enter لـ:",
    completeRecovery: "إكمال الاستلام",
    printFinalInvoice: "طباعة الفاتورة النهائية",
    createNewRepairDescription: "إنشاء أمر إصلاح جديد بتصميم حديث",
    selectClientRepair: "اختر إصلاح العميل (قيد التنفيذ)",
    selectedRepair: "الإصلاح المحدد",
    changeSelection: "تغيير الاختيار",
    repairCompletionInfo: "اختر إصلاحاً قيد التنفيذ لتحديده كمكتمل",
    noInProgressRepairs: "لا توجد إصلاحات قيد التنفيذ حالياً",
    createNewRepairFirst: "أنشئ إصلاحاً جديداً أولاً أو تحقق من حالات الإصلاح",
    chooseRepairOutcome: "اختر نتيجة الإصلاح لتحديث الحالة وفقاً لذلك",
    selectSuccessOrFailure: "اختر ما إذا كان الإصلاح نجح أم فشل",
    selectRepairToComplete: "اختر الإصلاح المراد إكماله",
    chooseInProgressRepair: "مسح رمز QR أو اختيار يدوياً",
    repairSuccessDescription: "تم إكمال الإصلاح بنجاح - سيتم تحديده كـ 'في انتظار العميل'",
    repairNotSuccessDescription: "لم يتم إكمال الإصلاح - سيتم تحديده كـ 'غير ناجح'",
    verificationPrice: "سعر التحقق (دج)",
    verificationPriceNote: "السعر المحدد لأعمال التشخيص/التحقق",
    failureReason: "سبب الفشل",
    reviewAndComplete: "مراجعة وإكمال",
    confirmDetailsAndComplete: "تحقق من التفاصيل وأكمل الاستلام",
    finalPrice: "السعر النهائي (إذا كان مختلفاً)",
    finalPriceNote: "اتركه فارغاً لاستخدام السعر المحسوب",
    completeRecovery: "إكمال الاستلام",
    printFinalInvoice: "طباعة الفاتورة النهائية",
    selectRepairFromList: "اختر إصلاحاً من القائمة...",
    orSearchManually: "أو ابحث يدوياً",
    searchByNamePhoneDevice: "البحث بالاسم أو الهاتف أو الجهاز...",
    searchByNamePhoneDate: "البحث بالاسم أو الهاتف أو التاريخ...",
    pricingBreakdown: "تفصيل الأسعار",
    noRepairsReady: "لا توجد إصلاحات جاهزة للاستلام",
    checkRepairStatuses: "تحقق من حالات الإصلاح أو أكمل الإصلاحات أولاً",
    back: "رجوع",
    terminerRecuperation: "إنهاء الاستلام",
    printingFinalInvoice: "طباعة الفاتورة النهائية",
    finalInvoice: "الفاتورة النهائية",
    completionDate: "تاريخ الإنجاز",
    repairCompleted: "تم إنجاز الإصلاح بنجاح",
    finalInvoicePrinted: "تم طباعة الفاتورة النهائية",
    finalPrice: "السعر النهائي (إذا كان مختلفاً)",
    finalPriceNote: "اتركه فارغاً لاستخدام السعر الإجمالي المحسوب",
    scanBarcodeOrQR: "مسح الباركود أو رمز QR",
    scanClientTicket: "امسح باركود أو رمز QR الخاص بتذكرة الإصلاح للعميل",
    barcodeInput: "إدخال ماسح الباركود",
    scanBarcodeHere: "امسح الباركود هنا أو اكتب يدوياً...",
    repairFoundByBarcode: "تم العثور على الإصلاح بالباركود",
    repairNotFoundByBarcode: "لم يتم العثور على إصلاح بهذا الباركود",
    suppliersPartsReparation: "موردي قطع الإصلاح",
    viewAllTransactions: "عرض جميع المعاملات",
    totalCredit: "إجمالي الائتمان",
    transactions: "المعاملات",
    noSuppliersPartsData: "لا توجد بيانات موردي القطع",
    viewTransactions: "عرض المعاملات",
    printTransactions: "طباعة المعاملات",
    doneSuccess: "تم → الحالة: 'في انتظار العميل'",
    doneNotSuccess: "تم → الحالة: 'في انتظار العميل'",
    repairMarkedAsNotSuccess: "تم تحديد الإصلاح كغير ناجح - في انتظار العميل",

    // Printing Messages
    printingRepairTicket: "طباعة تذكرة الإصلاح",
    printingQRCode: "طباعة رمز QR",
    repairTicketPrinted: "تم طباعة تذكرة الإصلاح",
    qrCodePrinted: "تم طباعة رمز QR",

    // Enhanced Thermal Printing Messages
    thermalPrintButton: "طباعة حرارية",
    printThermal: "طباعة حرارية",
    repairOrdersList: "قائمة أوامر الإصلاح",
    repairOrdersListPrinted: "تم طباعة قائمة أوامر الإصلاح",
    supplierTransactions: "معاملات المورد",
    supplierInformation: "معلومات المورد",
    transactionsList: "قائمة المعاملات",
    transactionCount: "عدد المعاملات",
    andMore: "والمزيد",
    clientPickupReceipt: "إيصال استلام العميل",
    repairSummary: "ملخص الإصلاح",
    repairStatus: "حالة الإصلاح",
    successful: "ناجح",
    notSuccessful: "غير ناجح",
    paymentDetails: "تفاصيل الدفع",
    finalAmount: "المبلغ النهائي",
    pickupTime: "وقت الاستلام",
    thankYouForTrust: "شكراً لثقتكم بنا",
    summary: "الملخص",
    totalOrders: "إجمالي الأوامر",
    printDate: "تاريخ الطباعة",
    totals: "الإجماليات",
    totalValue: "القيمة الإجمالية",
    reportGenerated: "تم إنشاء التقرير",
    repairReceipt: "إيصال الإصلاح",
    repairInformation: "معلومات الإصلاح",
    repairId: "رقم الإصلاح",
    pricingDetails: "تفاصيل الأسعار",

    // New Thermal Printing Translations
    repairTicket: "تذكرة الإصلاح",
    repairSystem: "نظام الإصلاح - ICALDZ",
    ticketNumber: "رقم التذكرة",
    printedOn: "طُبع في",
    clientInvoice: "فاتورة العميل",
    invoiceNumber: "رقم الفاتورة",
    time: "الوقت",
    paymentMode: "طريقة الدفع",
    cash: "نقداً",
    service: "الخدمة",
    qty: "الكمية",
    price: "السعر",
    total: "المجموع",
    repairParts: "قطع الإصلاح",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    finalTotal: "المجموع النهائي",
    thankYouVisit: "شكراً لزيارتكم",

    // Paste Ticket Translations
    printingPasteTicket: "طباعة تذكرة اللصق",
    pasteTicketPrinted: "تم طباعة تذكرة اللصق",
    printPasteTicket: "طباعة تذكرة اللصق",
    noDescription: "بدون وصف",
    interestRate: "معدل الفائدة",

    // Supplier Transaction Translations
    supplierTransactions: "معاملات المورد",
    supplierName: "اسم المورد",
    totalTransactions: "إجمالي المعاملات",
    description: "الوصف",
    partProblem: "مشكلة القطعة",
    partsPrice: "سعر القطع",
    date: "التاريخ",
    amount: "المبلغ",
    status: "الحالة",
    paid: "مدفوع",
    pending: "معلق",
    paidAmount: "المبلغ المدفوع",
    remainingCredit: "الائتمان المتبقي",
    thankYouBusiness: "شكراً لتعاملكم معنا",
    interestRate: "معدل الفائدة",

    // Additional Repair Translations
    manageRepairOrders: "إدارة أوامر الإصلاح وتتبع حالة الأجهزة",
    createNewRepairOrder: "إنشاء أمر إصلاح جديد",
    markRepairAsCompleted: "تحديد الإصلاح كمكتمل",
    processClientPickup: "معالجة استلام العميل",
    repairOrders: "أوامر الإصلاح",
    searchRepairs: "البحث في الإصلاحات...",
    allStatuses: "جميع الحالات",
    noRepairsFound: "لا توجد إصلاحات",
    pleaseEnterClientName: "يرجى إدخال اسم العميل",
    pleaseEnterDeviceName: "يرجى إدخال اسم الجهاز",
    pleaseSelectDeviceType: "يرجى اختيار نوع الجهاز",
    pleaseSelectProblemType: "يرجى اختيار نوع المشكلة",
    pleaseEnterValidRepairPrice: "يرجى إدخال سعر إصلاح صحيح",
    repairOrderCreated: "تم إنشاء أمر الإصلاح بنجاح",
    canOnlyEditCompletedRepairs: "يمكن تعديل الإصلاحات المكتملة فقط",
    repairMarkedAsCompleted: "تم تحديد الإصلاح كمكتمل",
    repairMarkedAsFailed: "تم تحديد الإصلاح كفاشل",
    remarksRequiredForFailure: "الملاحظات مطلوبة عند فشل الإصلاح",
    repairNotReadyForPickup: "الإصلاح غير جاهز للاستلام",
    repairNotFound: "لم يتم العثور على الإصلاح",
    invalidQRCode: "رمز QR غير صحيح",
    repairFound: "تم العثور على الإصلاح",
    enterClientName: "أدخل اسم العميل",
    enterDeviceName: "أدخل اسم الجهاز",
    selectDeviceType: "اختر نوع الجهاز",
    selectProblem: "اختر المشكلة",
    enterNewProblem: "أدخل المشكلة الجديدة",
    enterRemarks: "أدخل ملاحظات إضافية",
    autoGenerated: "يتم إنشاؤه تلقائياً",
    optional: "اختياري",
    explainFailureReason: "اشرح سبب فشل الإصلاح",
    markAsWaitingForClient: "تحديد كـ في انتظار العميل",
    markAsNotSuccess: "تحديد كـ غير ناجح",
    scanClientQRCode: "امسح رمز QR الخاص بالعميل لعرض معلومات الإصلاح",
    openQRScanner: "فتح ماسح QR",
    repairSummary: "ملخص الإصلاح",
    totalAmount: "المبلغ الإجمالي",
    completePickup: "إكمال الاستلام",
    printFinalInvoice: "طباعة الفاتورة النهائية",
    pointCameraAtQR: "وجه الكاميرا نحو رمز QR",
    orEnterManually: "أو أدخل يدوياً",
    enterRepairCode: "أدخل رمز الإصلاح",
    search: "بحث",

    // Info Modal Translations
    repairInformation: "معلومات الإصلاح",
    printA4: "طباعة A4",
    clientInformation: "معلومات العميل",
    deviceInformation: "معلومات الجهاز",
    pricingInformation: "معلومات التسعير",
    statusInformation: "معلومات الحالة",
    currentStatus: "الحالة الحالية",
    completionRemarks: "ملاحظات الإكمال",
    failureRemarks: "ملاحظات الفشل",
    timestamps: "الطوابع الزمنية",
    createdAt: "تاريخ الإنشاء",
    completedAt: "تاريخ الإكمال",
    pickedUpAt: "تاريخ الاستلام",
    printingError: "خطأ في الطباعة",
    thankYou: "شكراً لثقتكم بنا",
    cash: "نقداً",
    creditCard: "بطاقة ائتمان",
    bankTransfer: "تحويل بنكي",
    check: "شيك",
    otherPaymentMethod: "طريقة أخرى",

    // Supplier Modal
    addNewSupplier: "إضافة مورّد جديد",
    supplierNumber: "رقم المورد",
    supplierName: "اسم المورد",
    enterSupplierName: "أدخل اسم المورد",
    supplierPhone: "رقم الهاتف",
    supplierEmail: "البريد الإلكتروني",
    supplierAddress: "العنوان",
    fullAddress: "العنوان الكامل",
    saveSupplier: "حفظ المورد",

    // Supplier Management Modal
    suppliersManagement: "إدارة الموردين",
    addNewSupplierButton: "إضافة مورد جديد",
    supplierID: "رقم المورد",
    supplierNameHeader: "اسم المورد",
    supplierPhoneHeader: "رقم الهاتف",
    supplierEmailHeader: "البريد الإلكتروني",
    supplierAddressHeader: "العنوان",
    noSuppliersAdded: "لا توجد موردين مضافين",
    deleteSupplier: "حذف",
    editSupplier: "تعديل المورد",
    confirmDeleteSupplier: "هل أنت متأكد من حذف هذا المورد؟",
    supplierDeletedSuccessfully: "تم حذف المورد بنجاح",
    supplierUpdatedSuccessfully: "تم تحديث المورد بنجاح",
    pleaseEnterSupplierName: "يرجى إدخال اسم المورد",
    enterSupplierPhone: "أدخل رقم الهاتف",
    enterSupplierEmail: "أدخل البريد الإلكتروني",
    enterSupplierAddress: "أدخل العنوان",
    close: "إغلاق",

    // Messages
    fillRequiredFields: "يرجى ملء جميع الحقول المطلوبة",
    usernameExists: "اسم المستخدم موجود مسبقاً",
    sellerAddedSuccess: "تم إضافة البائع بنجاح!",
    confirmDeleteSeller: "هل أنت متأكد من حذف هذا البائع؟",
    sellerDeletedSuccess: "تم حذف البائع بنجاح",
    sellerStatusUpdated: "تم تحديث حالة البائع",
    storeSettingsSaved: "تم حفظ إعدادات المتجر بنجاح",
    expenseUpdatedSuccess: "تم تحديث المصروف بنجاح",
    expenseAddedSuccess: "تم إضافة المصروف بنجاح",

    // Edit Product Modal
    editProduct: "تعديل المنتج",
    productNumber: "رقم المنتج",
    productName: "اسم المنتج",
    enterProductName: "أدخل اسم المنتج",
    selectCategory: "اختر الفئة",
    categoryManagement: "إدارة الفئات",
    barcode: "الباركود",
    buyPrice: "سعر الشراء",
    sellPrice: "سعر البيع",
    currentStock: "الكمية الحالية",
    minStock: "الحد الأدنى",
    saveChanges: "حفظ التعديلات",

    // New Product in Purchase Modal
    addNewProduct: "إضافة منتج جديد",
    purchasedQuantity: "الكمية المشتراة",
    addProduct: "إضافة المنتج",
    enterBuyPrice: "أدخل سعر الشراء",
    enterSellPrice: "أدخل سعر البيع",
    enterInitialStock: "أدخل الكمية الأولية",
    enterMinStock: "أدخل الحد الأدنى",
    initialStock: "الكمية الأولية",
    redirectToInventory: "تم التوجه إلى إدارة المخزون لإضافة منتج جديد",
    addNewProductOpened: "تم فتح نافذة إضافة منتج جديد",

    // Edit Invoice Modal
    editInvoice: "تعديل الفاتورة",
    invoiceNumber: "رقم الفاتورة",
    customer: "العميل",
    invoiceItems: "عناصر الفاتورة",
    product: "المنتج",
    price: "السعر",
    quantity: "الكمية",
    total: "المجموع",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    finalTotal: "المجموع النهائي",

    // Return Products Modal
    returnProducts: "إرجاع منتجات من الفاتورة",
    invoiceDate: "تاريخ الفاتورة",
    originalTotal: "المجموع الأصلي",
    selectProductsToReturn: "تحديد المنتجات المراد إرجاعها",
    originalQuantity: "الكمية الأصلية",
    returnQuantity: "كمية الإرجاع",
    returnValue: "قيمة الإرجاع",
    totalReturnValue: "إجمالي قيمة الإرجاع",
    confirmReturn: "تأكيد الإرجاع",

    // Sales Invoice Modal - Additional Keys
    saveInvoiceShortcut: "حفظ الفاتورة",
    addProductShortcut: "إضافة منتج",
    closeWithoutSaving: "إغلاق بدون حفظ",
    selectRegisteredCustomer: "اختر زبون مسجل",
    scanBarcodeLabel: "مسح الباركود",
    scanBarcodePlaceholder: "امسح الباركود أو أدخله يدوياً",
    searchProductPlaceholder: "ابحث عن منتج (الاسم، الرمز، الباركود)...",
    selectProduct: "اختر منتج",
    available: "متوفر",
    noProductsAdded: "لم يتم إضافة أي منتجات بعد",
    useBarcodeOrSelect: "استخدم الباركود أو اختر من القائمة",
    invoiceItems: "عناصر الفاتورة",
    productName: "المنتج",
    action: "إجراء",
    deleteTitle: "حذف",
    finalTotal: "المجموع النهائي",
    saveInvoice: "حفظ الفاتورة",
    quantityRequiredExceedsStock: "الكمية المطلوبة أكبر من المتوفر",

    // Sales Invoice Barcode Scanner
    salesBarcodeScanner: "ماسح الباركود للمبيعات",
    scanToAddProduct: "امسح الباركود لإضافة منتج",
    barcodeActive: "ماسح الباركود نشط",
    clearBarcode: "مسح الباركود",
    productDisplay: "عرض المنتج",
    waitingForScan: "En attente du scan du code-barres...",
    activeBarcodeScanner: "نشط - مسح الباركود",
    clear: "مسح",

    // Toast Notifications
    clearAllNotifications: "مسح جميع الإشعارات",

    // Sales Invoice Popup Notifications
    pleaseAddProductsToInvoice: "يرجى إضافة منتجات للفاتورة",
    productNotFoundInSystem: "المنتج غير موجود",
    quantityExceedsStock: "الكمية المطلوبة أكبر من المتوفر",
    salesInvoiceSavedSuccessfully: "تم حفظ فاتورة المبيعات رقم",
    successfullyAndStockUpdated: "بنجاح وتم تحديث المخزون!",
    invoiceSentToThermalPrinter: "تم إرسال الفاتورة للطباعة الحرارية",
    discountApplied: "تم تطبيق خصم",
    fromProfitMarginForCustomer: "من هامش الربح للزبون",
    forWalkInCustomer: "لزبون عابر",
    forCustomer: "للزبون",
    invoicePrintedAutomatically: "تم طباعة الفاتورة تلقائياً",

    // Keyboard Shortcuts Notifications
    f1NewSalesInvoiceOpened: "تم فتح فاتورة مبيعات جديدة",
    f2NewInvoiceOpened: "تم فتح فاتورة جديدة",
    f3ProductAdded: "تم إضافة المنتج",
    f3ProductAddedToPurchase: "تم إضافة المنتج للمشتريات",
    f3OnlyAvailableInInvoice: "متاح فقط في نافذة الفاتورة",
    f6NewProductOpened: "تم فتح نافذة إضافة منتج جديد",
    f6NavigatedToInventory: "تم الانتقال لإدارة المخزون وفتح نافذة إضافة منتج جديد",
    f7NewPurchaseInvoiceOpened: "تم فتح فاتورة مشتريات جديدة",
    f7NavigatedToPurchases: "تم الانتقال لإدارة المشتريات وفتح فاتورة مشتريات جديدة",

    // Product Management Notifications
    productAddedToInvoice: "تم إضافة",
    toInvoice: "للفاتورة",
    productUpdatedSuccessfully: "تم تحديث المنتج",
    successfully: "بنجاح",
    productAddedWithScannedBarcode: "تم إضافة المنتج",
    withScannedBarcode: "بالباركود المسحوب:",
    productAddedWithAutoBarcode: "تم إضافة المنتج",
    withAutoBarcode: "بباركود تلقائي:",
    confirmDeleteProduct: "هل أنت متأكد من حذف المنتج",
    productDeletedSuccessfully: "تم حذف المنتج",

    // Supplier Management Notifications
    pleaseEnterSupplierName: "يرجى إدخال اسم المورد",
    supplierAddedSuccessfully: "تم إضافة المورد بنجاح",
    confirmDeleteSupplier: "هل أنت متأكد من حذف هذا المورد؟",
    supplierDeletedSuccessfully: "تم حذف المورد بنجاح",

    // System Control Notifications
    soundEnabled: "تم تفعيل الأصوات",
    soundDisabled: "تم إيقاف الأصوات",
    keyboardShortcutsInfo: "الاختصارات مفعلة: F1 فاتورة مبيعات، F2 فاتورة جديدة، F3 إضافة، F6 منتج جديد، F7 فاتورة مشتريات، Enter حفظ، ESC إغلاق",
    keyboardShortcutsEnabled: "تم تفعيل اختصارات لوحة المفاتيح",
    keyboardShortcutsDisabled: "تم إيقاف اختصارات لوحة المفاتيح",
    enableKeyboardShortcuts: "تفعيل اختصارات لوحة المفاتيح",
    disableKeyboardShortcuts: "إيقاف اختصارات لوحة المفاتيح",
    printerEnabled: "تم تفعيل الطابعة",
    printerDisabled: "تم إيقاف الطابعة",
    notificationsEnabled: "تم تفعيل الإشعارات",
    notificationsDisabled: "تم إيقاف الإشعارات",
    enableNotifications: "تفعيل الإشعارات",
    disableNotifications: "إيقاف الإشعارات",
    addProduct: "إضافة منتج",
    scrollToTop: "العودة إلى الأعلى",
    saveInvoice: "حفظ الفاتورة",

    // Credit Limit Validation
    creditLimitExceeded: "تم تجاوز حد الائتمان",
    currentBalance: "الرصيد الحالي",
    invoiceAmount: "مبلغ الفاتورة",
    newBalance: "الرصيد الجديد",
    exceededAmount: "المبلغ المتجاوز",

    // Bulk Selection
    selectAll: "تحديد الكل",
    deselectAll: "إلغاء تحديد الكل",
    deleteSelected: "حذف المحدد",
    pleaseSelectItems: "يرجى تحديد عناصر للحذف",
    confirmDeleteSelected: "هل أنت متأكد من حذف العناصر المحددة؟",
    selectedItemsDeleted: "تم حذف العناصر المحددة",
    itemsSelected: "عنصر محدد",
    notAllowedManagerOnlyBulkDelete: "غير مسموح - المدير فقط يمكنه الحذف المتعدد",
    andStockRestored: "وإرجاع المنتجات للمخزون",
    stockWillBeAdjusted: "سيتم تعديل المخزون تلقائياً",
    andStockAdjusted: "وتعديل المخزون",

    // Invoice Management Notifications
    invoiceDeletedAndStockRestored: "تم حذف الفاتورة",
    andStockRestored: "وإرجاع المنتجات للمخزون",
    notAllowedManagerOnly: "غير مسموح - المدير فقط يمكنه تعديل المخزون",
    stockQuantityUpdated: "تم تحديث الكمية بنجاح",
    productRemovedFromInvoice: "تم حذف",
    fromInvoice: "من الفاتورة",
    productRemovedFromPurchase: "تم حذف المنتج من الفاتورة",
    confirmDeleteInvoice: "هل أنت متأكد من حذف الفاتورة رقم",
    allProductsWillBeRestored: "سيتم إرجاع جميع المنتجات إلى المخزون.",
    confirmDeletePurchaseInvoice: "هل أنت متأكد من حذف فاتورة المشتريات",
    purchaseInvoiceDeletedAndStockAdjusted: "تم حذف فاتورة المشتريات وإعادة تعديل المخزون",
    invoiceUpdatedAndStockAdjusted: "تم تحديث الفاتورة",
    andStockAdjusted: "وتحديث المخزون",
    confirmDeleteAllData: "هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه!",
    allInventoryDataDeleted: "تم حذف جميع بيانات المخزون",
    reportSentToPrinter: "تم إرسال التقرير للطباعة",

    // Purchase Management Notifications
    pleaseSelectProduct: "يرجى اختيار منتج",
    pleaseEnterValidQuantity: "يرجى إدخال كمية صحيحة",
    pleaseEnterValidPrice: "يرجى إدخال سعر صحيح",
    pleaseSelectSupplier: "يرجى اختيار مورد",
    purchaseInvoiceOpenedForEdit: "تم فتح فاتورة المشتريات للتعديل",
    notAllowedManagerOnlyPurchase: "غير مسموح - المدير فقط يمكنه تعديل فواتير المشتريات",
    notAllowedManagerOnlyDeletePurchase: "غير مسموح - المدير فقط يمكنه حذف فواتير المشتريات",
    generalSupplier: "مورد عام",

    // Category Management Notifications
    pleaseEnterCategoryName: "يرجى إدخال اسم الفئة",
    categoryAlreadyExists: "الفئة موجودة مسبقاً",
    categoryAddedSuccessfully: "تم إضافة الفئة",

    // Customer Management Notifications
    pleaseEnterCustomerName: "يرجى إدخال اسم الزبون",
    customerAddedSuccessfully: "تم إضافة الزبون بنجاح",
    customerDeletedSuccessfully: "تم حذف الزبون بنجاح",
    customerDataUpdatedSuccessfully: "تم تحديث بيانات الزبون بنجاح",

    // Product Management Notifications
    pleaseEnterProductName: "يرجى إدخال اسم المنتج",


    scanBarcodeToAddProduct: "امسح الباركود - اضغط Enter لفتح الفاتورة",
    openInvoiceWithProduct: "فتح الفاتورة مع المنتج",
    openInvoice: "فتح الفاتورة",
    dashboardBarcodeHelp: "امسح الباركود لعرض المعلومات، اضغط Enter أو زر فتح الفاتورة لإضافة المنتج للفاتورة",
    scanProductToDisplay: "امسح منتج لعرض المعلومات",
    productInfo: "معلومات المنتج",

    // NEW: Barcode Scanning Messages
    productOutOfStock: "المنتج غير متوفر في المخزون",
    quantityExceedsStock: "الكمية تتجاوز المتوفر",
    available: "متوفر",
    quantityIncreased: "تم زيادة الكمية",
    productAdded: "تم إضافة المنتج",
    productNotFound: "لم يتم العثور على المنتج بالباركود",
    barcodeMinLength: "الباركود قصير جداً - يجب أن يكون 3 أحرف على الأقل",
    noInvoiceBeingEdited: "لا توجد فاتورة قيد التعديل",

    // Barcode & Product Search Notifications
    productAddedToInvoiceAutomatically: "تم إضافة",
    toInvoiceAutomatically: "للفاتورة تلقائياً",
    productFoundWithBarcode: "تم العثور على",
    noProductFoundWithBarcode: "لم يتم العثور على منتج بهذا الباركود",
    autoBarcodeGenerated: "تم توليد باركود تلقائي",
    barcodeCleared: "تم مسح الباركود",
    barcodeAlreadyExists: "الباركود موجود مسبقاً في منتج آخر",

    // Login & Authentication Notifications
    welcomeUser: "مرحباً بك",
    accountInactiveContactManager: "حسابك غير نشط، يرجى التواصل مع المدير",
    invalidUsernameOrPassword: "اسم المستخدم أو كلمة المرور غير صحيحة",

    // Invoice Details Modal
    invoiceDetails: "تفاصيل الفاتورة",
    salesInvoice: "فاتورة مبيعات",
    invoiceInfo: "معلومات الفاتورة",
    customerInfo: "معلومات الزبون",
    customerName: "اسم الزبون",
    creationTime: "وقت الإنشاء",

    // Thermal Printing
    invoiceNumberLabel: "فاتورة رقم:",
    dateLabel: "التاريخ:",
    timeLabel: "الوقت:",
    customerLabel: "الزبون:",
    paymentMethodLabel: "طريقة الدفع:",
    productsLabel: "المنتجات",
    subtotalLabel: "المجموع الفرعي:",
    discountLabel: "الخصم:",
    taxLabel: "الضريبة",
    finalTotalLabel: "المجموع النهائي:",
    thankYouMessage: "شكراً لزيارتكم",
    printedAtLabel: "طُبعت في:",
    salesInvoiceTitle: "فاتورة مبيعات",
    accountingSystem: "نظام المحاسبي",
    developedBy: "تم التطوير بواسطة",

    // Enhanced Thermal Printing
    thermalInvoice: "فاتورة حرارية",
    thermalPrintError: "خطأ في الطباعة الحرارية",
    printError: "خطأ في الطباعة",
    popupBlocked: "تم حظر النافذة المنبثقة - يرجى السماح بالنوافذ المنبثقة",
    directPrintingEnabled: "تم تفعيل الطباعة المباشرة",
    thermalPrinterDetected: "تم اكتشاف طابعة حرارية",
    thermalPrinterNotDetected: "لم يتم اكتشاف طابعة حرارية",
    printingDirectly: "جاري الطباعة مباشرة...",
    printingSentToThermal: "تم إرسال الطباعة للطابعة الحرارية",
    storeNumber: "رقم المتجر",
    qtyColumn: "QNT",

    // Invoice Details Modal - Complete
    invoiceNumberColon: "رقم الفاتورة:",
    dateColon: "التاريخ:",
    creationTimeColon: "وقت الإنشاء:",
    customerInfoTitle: "معلومات العميل",
    customerNameColon: "اسم العميل:",
    paymentMethodColon: "طريقة الدفع:",
    productsTitle: "المنتجات",
    productColumn: "المنتج",
    quantityColumn: "الكمية",
    priceColumn: "السعر",
    totalColumn: "المجموع",
    noProductsInInvoice: "لا توجد منتجات في هذه الفاتورة",
    subtotalColon: "المجموع الفرعي:",
    taxColon: "الضريبة",
    discountColon: "الخصم:",
    finalTotalColon: "المجموع النهائي:",
    normalPrintButton: "طباعة عادية",
    thermalPrintButton: "طباعة حرارية",
    closeButton: "إغلاق",
    backToTop: "العودة للأعلى",

    // Return Process Notifications
    pleaseSelectAtLeastOneProduct: "يرجى تحديد كمية الإرجاع لمنتج واحد على الأقل",
    productsReturnedSuccessfully: "تم إرجاع المنتجات",
    andStockUpdated: "وتحديث المخزون",

    // Product Modal - Additional Keys
    editExistingProduct: "تعديل منتج موجود",
    scanBarcodeOrEnter: "امسح الباركود أو أدخله يدوياً - سيتم التوليد التلقائي إذا تُرك فارغاً",
    generateAutoBarcode: "توليد باركود تلقائي",
    clearBarcode: "مسح الباركود",
    barcodeHelp: "يمكنك مسح الباركود باستخدام قارئ الباركود أو إدخاله يدوياً. إذا كان الباركود موجود في المخزون، سيتم عرض معلومات المنتج للتعديل. إذا تُرك فارغاً، سيتم توليد باركود تلقائياً عند الحفظ.",
    currentBarcode: "📷 الباركود الحالي",
    barcodeWillShow: "سيتم عرض الباركود هنا",
    currentQuantity: "الكمية الحالية",
    saveProduct: "حفظ المنتج",
    manageCategories: "إدارة الفئات",
    productNameAlreadyExists: "اسم المنتج موجود بالفعل",
    pleaseChooseDifferentName: "يرجى اختيار اسم مختلف",
    productExistsWithBarcode: "يوجد منتج بهذا الباركود",
    checkBeforeSaving: "تحقق قبل الحفظ",
    barcodeAlreadyExists: "الباركود موجود بالفعل",
    usedByProduct: "مستخدم بواسطة المنتج",
    customerNameAlreadyExists: "اسم الزبون موجود بالفعل",
    supplierNameAlreadyExists: "اسم المورد موجود بالفعل",
    sellerNameAlreadyExists: "اسم البائع موجود بالفعل",
    phoneNumberAlreadyExists: "رقم الهاتف موجود بالفعل",
    usedByCustomer: "مستخدم بواسطة الزبون",
    usedBySupplier: "مستخدم بواسطة المورد",
    usedBySeller: "مستخدم بواسطة البائع",
    productFoundMessage: "تم العثور على المنتج",
    canEditAndSave: "يمكنك تعديل المعلومات والحفظ",
    barcodeScannedSuccess: "تم مسح الباركود بنجاح - باركود جديد",
    barcodeAvailable: "الباركود متاح للاستخدام - منتج جديد",

    // Category Management Modal
    addNewCategory: "إضافة فئة جديدة",
    newCategoryName: "اسم الفئة الجديدة",
    existingCategories: "الفئات الموجودة",
    edit: "تعديل",

    // Toast Messages for Products
    productFoundMessage: "تم العثور على المنتج",
    canEditAndSave: "يمكنك تعديل المعلومات والحفظ",
    barcodeScannedSuccess: "تم مسح الباركود بنجاح - باركود جديد",
    barcodeAvailable: "الباركود متاح للاستخدام - منتج جديد",
    autoBarcodeGenerated: "تم توليد باركود تلقائي",
    barcodeCleared: "تم مسح الباركود",

    // Messages
    loginSuccess: "تم تسجيل الدخول بنجاح",
    loginError: "خطأ في اسم المستخدم أو كلمة المرور",
    invoiceSaved: "تم حفظ الفاتورة بنجاح",
    customerAdded: "تم إضافة الزبون بنجاح",
    customerDeleted: "تم حذف الزبون بنجاح",
    productAdded: "تم إضافة المنتج بنجاح",
    settingsSaved: "تم حفظ الإعدادات بنجاح",
    pleaseSelectCustomer: "يرجى اختيار زبون",
    pleaseAddProducts: "يرجى إضافة منتجات للفاتورة",
    productNotFound: "لم يتم العثور على منتج بهذا الباركود",

    // Activation Dialog
    activationTitle: "تفعيل البرنامج",
    activationDescription: "يرجى إدخال كود التفعيل الخاص بك لتفعيل البرنامج مدى الحياة",
    activationCodeLabel: "كود التفعيل:",
    activationCodePlaceholder: "ICAL-2025-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX",
    activateProgram: "تفعيل البرنامج",
    activating: "جاري التفعيل...",
    resetActivation: "إعادة تعيين التفعيل (للاختبار)",
    resetActivationTooltip: "إعادة تعيين التفعيل للاختبار",
    deviceInfo: "معلومات الجهاز:",
    deviceId: "معرف الجهاز:",
    deviceNote: "سيتم ربط التفعيل بهذا الجهاز بشكل دائم",
    getActivationCode: "للحصول على كود التفعيل:",
    phone: "الهاتف:",
    email: "البريد:",
    website: "الموقع:",

    // Activation Error Messages
    pleaseEnterActivationCode: "يرجى إدخال كود التفعيل",
    unexpectedActivationError: "حدث خطأ غير متوقع أثناء التفعيل",
    programAlreadyActivated: "البرنامج مفعل بالفعل على هذا الجهاز\n\n💡 للاختبار: اكتب \"reset\" أو \"test\" لإظهار خيار إعادة التعيين",
    confirmResetActivation: "هل أنت متأكد من إعادة تعيين التفعيل؟\n\nسيتم حذف التفعيل الحالي ويمكنك اختبار كود جديد.",
    resetActivationSuccess: "✅ تم إعادة تعيين التفعيل بنجاح!\n\nيمكنك الآن اختبار كود تفعيل جديد.",
    invalidActivationCodeFormat: "تنسيق كود التفعيل غير صحيح",
    invalidActivationCodeFormat: "تنسيق كود التفعيل غير صحيح",
    invalidActivationCode: "كود التفعيل غير صحيح",
    codeAlreadyUsed: "تم استخدام هذا الكود من قبل\n\n⚠️ كل كود تفعيل يمكن استخدامه مرة واحدة فقط",
    invalidOrExpiredCode: "كود التفعيل غير صالح أو منتهي الصلاحية",
    activationValidationError: "خطأ في التحقق من كود التفعيل",

    // Reports and Statistics
    reportsAndStatistics: "التقارير والإحصائيات",
    comprehensiveReports: "تقارير شاملة لجميع العمليات المالية والتجارية - المدير فقط",

    // Financial Reports
    financialReports: "التقارير المالية",
    financialReportsDesc: "تقارير المبيعات والمشتريات والأرباح",
    salesReport: "تقرير المبيعات",
    salesReportDesc: "إجمالي المبيعات والفواتير",
    purchaseReport: "تقرير المشتريات",
    purchaseReportDesc: "إجمالي المشتريات والموردين",
    profitReport: "تقرير الأرباح",
    profitReportDesc: "تحليل الأرباح والخسائر",

    // Inventory Reports
    inventoryReports: "تقارير المخزون",
    inventoryReportsDesc: "حالة المخزون والمنتجات",
    generalInventoryReport: "تقرير المخزون العام",
    generalInventoryReportDesc: "جميع المنتجات وحالة المخزون",
    lowStockReport: "تقرير المخزون المنخفض",
    lowStockReportDesc: "المنتجات التي تحتاج إعادة تموين",
    outOfStockReport: "تقرير المنتجات النافدة",
    outOfStockReportDesc: "المنتجات غير المتوفرة",

    // Customer Reports
    customerReports: "تقارير العملاء",
    customerReportsDesc: "تحليل العملاء والمبيعات",
    customersReport: "تقرير العملاء",
    customersReportDesc: "قائمة العملاء وبياناتهم",
    debtorsReport: "تقرير المدينين",
    debtorsReportDesc: "العملاء المدينين والمستحقات",
    topCustomersReport: "أفضل العملاء",
    topCustomersReportDesc: "العملاء الأكثر شراءً",
    customerAnalysisReport: "تحليل العملاء",
    customerAnalysisReportDesc: "إحصائيات مفصلة للعملاء",

    // Performance Reports
    performanceReports: "تقارير الأداء",
    performanceReportsDesc: "مؤشرات الأداء والإحصائيات",
    dailyReport: "التقرير اليومي",
    dailyReportDesc: "ملخص العمليات اليومية",
    weeklyReport: "التقرير الأسبوعي",
    weeklyReportDesc: "ملخص العمليات الأسبوعية",
    monthlyReport: "التقرير الشهري",
    monthlyReportDesc: "ملخص العمليات الشهرية",
    yearlyReport: "التقرير السنوي",
    yearlyReportDesc: "ملخص العمليات السنوية",
    kpiReport: "مؤشرات الأداء",
    kpiReportDesc: "KPIs ومقاييس الأداء",

    // Report Statistics Labels
    totalDebts: "إجمالي الديون",
    activeCustomer: "عميل نشط",
    today: "اليوم",
    currentWeek: "الأسبوع الحالي",
    currentMonth: "الشهر الحالي",
    currentYear: "السنة الحالية",
    analysis: "تحليل",

    // Additional Reports
    inventoryValueReport: "تقرير قيمة المخزون",
    inventoryValueReportDesc: "القيمة الإجمالية للمخزون",
    categoriesReport: "تقرير الفئات",
    categoriesReportDesc: "تحليل المنتجات حسب الفئات",
    advancedReports: "تقارير متقدمة",
    advancedReportsDesc: "تقارير مفصلة مع فلاتر متقدمة",
    monthlyReport: "التقرير الشهري",
    monthlyReportDesc: "ملخص العمليات الشهرية",
    quickSummary: "ملخص سريع",
    totalSales: "إجمالي المبيعات",
    totalPurchases: "إجمالي المشتريات",
    netProfit: "صافي الربح",
    inventoryValue: "قيمة المخزون",
    totalValue: "القيمة الإجمالية",
    category: "فئة",

    // Advanced Reports Form Labels
    reportType: "نوع التقرير:",
    date: "التاريخ:",
    month: "الشهر:",
    daily: "يومي",
    monthly: "شهري",
    print: "طباعة",

    // Footer Translations
    reportGeneratedBy: "تم إنشاء هذا التقرير تلقائياً بواسطة نظام المحاسبي المصمم من طرف iCode DZ",
    allRightsReserved: "© 2025 iDesign DZ +213 551 93 05 89 - جميع الحقوق محفوظة",

    // New Report System Translations
    newSystem: "نظام جديد",
    accurateCalculations: "حسابات دقيقة 100%",
    costOfGoodsSold: "تكلفة البضاعة المباعة",
    grossProfit: "الربح الإجمالي",
    beforeExpenses: "قبل المصاريف",
    operatingExpenses: "المصاريف التشغيلية",
    dailyNetProfit: "صافي الربح اليومي",
    profit: "ربح",
    loss: "خسارة",
    fromSales: "من المبيعات",
    processedItems: "تم معالجة",
    from: "من",
    item: "عنصر",
    expense: "مصروف",
    dailyReportOpenedWithWarnings: "📊 تم فتح التقرير اليومي الجديد مع تحذيرات - يرجى مراجعة البيانات",
    dailyReportOpenedSuccessfully: "📊 تم فتح التقرير اليومي الجديد بنجاح - حسابات دقيقة 100%",
    annualOperationsSummary: "ملخص العمليات السنوية",
    improvedNewSystem: "نظام جديد محسن",
    noErrors: "بدون أخطاء",
    annualNetProfit: "صافي الربح السنوي",
    margin: "هامش",
    monthlyPerformance: "الأداء الشهري",
    sales: "المبيعات",
    cost: "التكلفة",
    annualReportOpenedSuccessfully: "📊 تم فتح التقرير السنوي الجديد بنجاح - حسابات دقيقة 100%",
    monthlyOperationsSummary: "ملخص العمليات الشهرية",
    monthlyNetProfit: "صافي الربح الشهري",
    dailyPerformanceForMonth: "الأداء اليومي للشهر",
    monthlyReportOpenedSuccessfully: "📅 تم فتح التقرير الشهري الجديد بنجاح - حسابات دقيقة 100%",
    invalidDataReload: "❌ Invalid data, please reload the page",
    reportValidationFailed: "⚠️ Report calculation validation failed",
    missingProductsWarning: "⚠️ Warning: missing products",
    dailyReportGenerated: "📊 تم إنشاء التقرير اليومي",
    monthlyReportGenerated: "📊 تم إنشاء التقرير الشهري",
    generateReportFirst: "⚠️ يرجى إنشاء التقرير أولاً",
    reportOpenedForPrint: "🖨️ تم فتح التقرير للطباعة",

    // Report Toast Messages
    salesReportOpened: "📈 تم فتح تقرير المبيعات للطباعة",
    purchaseReportOpened: "📉 تم فتح تقرير المشتريات للطباعة",
    profitReportOpened: "💎 تم فتح تقرير الأرباح للطباعة",
    inventoryValueReportOpened: "💰 تم فتح تقرير قيمة المخزون للطباعة",
    cashFlowReportOpened: "💸 تم فتح تقرير التدفق النقدي للطباعة",
    categoriesReportOpened: "🏷️ تم فتح تقرير الفئات للطباعة",
    customersReportOpened: "👤 تم فتح تقرير العملاء للطباعة",
    debtorsReportOpened: "💳 تم فتح تقرير المدينين للطباعة",
    topCustomersReportOpened: "🌟 تم فتح تقرير أفضل العملاء للطباعة",
    customerAnalysisReportOpened: "📊 تم فتح تحليل العملاء المتقدم للطباعة",
    kpiReportOpened: "🎯 تم فتح تقرير مؤشرات الأداء للطباعة",

    // Advanced Reports Content Translations
    creationDate: "تاريخ الإنشاء",
    soldProductsDetails: "تفاصيل المنتجات المباعة",
    totalCost: "إجمالي التكلفة",

    // KPI Report Translations
    kpiDashboard: "لوحة قيادة مؤشرات الأداء",
    performanceOverview: "نظرة عامة على الأداء",
    allPeriods: "جميع الفترات",
    margin: "هامش",
    growth: "نمو",
    financialPerformance: "الأداء المالي",
    customerMetrics: "مقاييس العملاء",
    inventoryMetrics: "مقاييس المخزون",
    operationalMetrics: "المقاييس التشغيلية",
    uniqueCustomers: "العملاء الفريدون",
    averageOrderValue: "متوسط قيمة الطلب",
    customersWithDebt: "العملاء المدينون",
    inventoryTurnover: "دوران المخزون",
    averageDailySales: "متوسط المبيعات اليومية",
    salesVelocity: "سرعة المبيعات",
    cashRatio: "نسبة النقد",
    monthlyGrowthRate: "معدل النمو الشهري",
    excellent: "ممتاز",
    good: "جيد",
    warning: "تحذير",

    // Debtors Report Translations
    debtorsAndReceivables: "المدينين والمستحقات",
    totalOutstandingAmount: "إجمالي المبالغ المستحقة",
    followUpRequired: "يجب متابعة تحصيل هذه المبالغ",
    debtorCustomers: "عدد العملاء المدينين",
    outstandingInvoices: "عدد الفواتير المستحقة",
    averageDebtPerCustomer: "متوسط الدين للعميل",
    highestOutstandingAmount: "أكبر مبلغ مستحق",
    customerName: "اسم العميل",
    invoiceCount: "عدد الفواتير",
    totalOutstanding: "إجمالي المبلغ المستحق",
    oldestInvoice: "أقدم فاتورة",
    newestInvoice: "أحدث فاتورة",
    priorityLevel: "مستوى الأولوية",
    high: "عالية",
    medium: "متوسطة",
    low: "منخفضة",

    // Top Customers Report Translations
    topCustomers: "أفضل العملاء",
    topCustomersAnalysis: "تحليل العملاء المميزين",
    topCustomer: "العميل الأول",
    totalSalesTopTen: "إجمالي مبيعات أفضل 10 عملاء",
    averagePurchasesTopCustomer: "متوسط المشتريات للعميل المميز",
    totalInvoicesCount: "إجمالي الفواتير",
    percentageOfTotalSales: "نسبة من إجمالي المبيعات",
    ranking: "الترتيب",
    totalPurchases: "إجمالي المشتريات",
    averageInvoice: "متوسط الفاتورة",
    firstPurchase: "أول شراء",
    lastPurchase: "آخر شراء",
    membershipPeriod: "فترة العضوية",
    days: "يوم",

    // Customer Analysis Report Translations
    advancedCustomerAnalysis: "تحليل العملاء المتقدم",
    businessIntelligence: "ذكاء الأعمال",
    generalCustomerStats: "إحصائيات العملاء العامة",
    newCustomersThisMonth: "عملاء جدد هذا الشهر",
    customersWithPurchases: "عملاء لديهم مشتريات",
    debtorCustomersCount: "عملاء مدينون",
    conversionRate: "معدل التحويل",
    purchaseFrequencyAnalysis: "تحليل تكرار الشراء",
    oneTimeBuyers: "مشترون لمرة واحدة",
    occasionalBuyers: "مشترون أحياناً",
    regularBuyers: "مشترون منتظمون",
    frequentBuyers: "مشترون دائمون",
    insightsAndRecommendations: "رؤى وتوصيات",
    retentionRate: "معدل الاحتفاظ",
    customersReturnToBuy: "من العملاء يعودون للشراء مرة أخرى",
    growthOpportunity: "فرصة النمو",
    oneTimePurchaseOpportunity: "عميل اشترى مرة واحدة فقط - فرصة لحملات إعادة الاستهداف",
    loyalCustomers: "العملاء المخلصون",
    regularPurchasers: "عميل يشترون بانتظام - يجب الاهتمام بهم وتقديم عروض خاصة",
    customerGrowth: "نمو العملاء",
    newCustomersThisMonthInsight: "عميل جديد هذا الشهر",
    positiveGrowth: "- نمو إيجابي",
    needsMarketingImprovement: "- يحتاج تحسين التسويق",
    debtManagement: "إدارة الديون",
    debtFollowUpNeeded: "عميل لديه ديون - يحتاج متابعة للتحصيل",

    // Category Report Translations
    categoriesReport: "تقرير الفئات",
    categoryAnalysis: "تحليل الفئات",
    productCount: "عدد المنتجات",
    totalQuantities: "إجمالي الكميات",
    totalValue: "القيمة الإجمالية",
    inventoryPercentage: "النسبة من المخزون",

    // Excel Export Translations
    productCode: "رمز المنتج",
    productName: "اسم المنتج",
    barcode: "الباركود",
    buyPrice: "سعر الشراء",
    sellPrice: "سعر البيع",
    availableQuantity: "الكمية المتوفرة",
    minStock: "الحد الأدنى",
    currency: "دج",
    status: "الحالة",
    lowStock: "منخفض",
    outOfStock: "نفد",
    highStock: "مرتفع",
    normalStock: "عادي",
    summary: "الملخص",
    totalProducts: "إجمالي المنتجات",
    lowStockProducts: "منتجات منخفضة المخزون",
    outOfStockProducts: "منتجات نفدت",
    inventoryReport: "تقرير المخزون",

    // Additional KPI Translations
    profitFromSales: "نسبة الربح من المبيعات",
    cashVsCredit: "المبيعات النقدية مقابل الآجلة",
    todaySales: "مبيعات اليوم",
    monthlySales: "مبيعات الشهر",
    netProfit: "صافي الربح",
    profitMargin: "هامش الربح",
    totalCustomers: "إجمالي العملاء",
    activeCustomers: "العملاء النشطون",
    inventoryValue: "قيمة المخزون",
    lowStockItems: "منتجات منخفضة المخزون",
    outOfStockItems: "منتجات نفدت",
    monthlyGrowthRate: "معدل النمو الشهري",

    // Advanced Report Display Translations
    topProducts: "أفضل المنتجات مبيعاً",
    quantitySold: "الكمية المباعة",
    totalRevenue: "إجمالي الإيرادات",
    profit: "الربح",

    // Additional KPI Detail Translations
    uniqueCustomersDesc: "عدد العملاء الفريدين",
    customersWithDebtDesc: "عدد العملاء الذين لديهم ديون",
    totalDebtDesc: "المبالغ المستحقة من العملاء",
    averageOrdersPerCustomer: "متوسط الطلبات لكل عميل",
    purchaseFrequency: "معدل تكرار الشراء",
    largeCustomerBase: "قاعدة عملاء كبيرة",
    mediumCustomerBase: "قاعدة عملاء متوسطة",
    smallCustomerBase: "قاعدة عملاء صغيرة",
    lowRatio: "نسبة منخفضة",
    mediumRatio: "نسبة متوسطة",
    highRatio: "نسبة عالية",
    highLoyalty: "ولاء عالي",
    mediumLoyalty: "ولاء متوسط",
    lowLoyalty: "ولاء منخفض",
    totalGoodsValue: "إجمالي قيمة البضائع",
    inventoryInvestment: "استثمار المخزون",
    needsRestocking: "منتجات تحتاج إعادة تموين",
    unavailableProducts: "منتجات غير متوفرة",
    stockSalesSpeed: "سرعة بيع المخزون",
    goodStock: "مخزون جيد",
    needsMonitoring: "يحتاج متابعة",
    urgentRestocking: "يحتاج تموين عاجل",
    none: "لا توجد",
    few: "قليلة",
    many: "كثيرة",
    fastTurnover: "دوران سريع",
    mediumTurnover: "دوران متوسط",
    slowTurnover: "دوران بطيء",
    excellentProfitMargin: "هامش ربح ممتاز يدل على كفاءة التسعير",
    improveProfitMargin: "يمكن تحسين هامش الربح بمراجعة التكاليف والأسعار",
    customerManagement: "إدارة العملاء",
    goodDebtManagement: "إدارة جيدة للديون والائتمان",
    improveCreditPolicies: "يحتاج تحسين سياسات الائتمان ومتابعة التحصيل",
    inventoryManagement: "إدارة المخزون",
    appropriateStockLevel: "مستوى مخزون مناسب",
    improveInventoryPlanning: "يحتاج تحسين تخطيط المخزون وإعادة التموين",
    growth: "النمو",
    thisMonth: "هذا الشهر",
    needsGrowthStrategies: "يحتاج استراتيجيات لتحفيز النمو",
    cashFlow: "التدفق النقدي",
    healthyCashFlow: "تدفق نقدي صحي مع نسبة مبيعات نقدية جيدة",
    improveCashFlow: "يحتاج تحسين التدفق النقدي وتقليل المبيعات الآجلة",

    // Cash Flow Report
    cashFlowReport: "تقرير التدفق النقدي",
    cashFlowReportDesc: "حركة النقد الداخل والخارج",
    cashMovement: "حركة النقد الداخل والخارج",
    cash: "نقداً",
    inCash: "نقداً",
    accountingSystem: "نظام المحاسبي",
    reportDate: "تاريخ التقرير",
    cashManagement: "إدارة النقدية",

    // Common Report Labels
    cashSales: "المبيعات النقدية",
    creditSales: "المبيعات الآجلة",
    cashPurchases: "المشتريات النقدية",
    creditPurchases: "المشتريات الآجلة",
    netCashFlow: "صافي التدفق النقدي",
    cashInflow: "نقد داخل",
    cashOutflow: "نقد خارج",
    cashSurplus: "فائض نقدي",
    cashDeficit: "عجز نقدي",
    receivables: "ذمم مدينة",
    payables: "ذمم دائنة",
    invoiceNumber: "رقم الفاتورة",
    customerName: "اسم الزبون",
    paymentMethod: "طريقة الدفع",
    subtotal: "المبلغ الفرعي",
    tax: "الضريبة",
    discount: "الخصم",
    finalAmount: "المبلغ النهائي",
    status: "الحالة",
    paid: "مدفوعة",
    debt: "دين",
    walkInCustomer: "زبون عابر",
    salesManagement: "إدارة المبيعات",
    businessManagement: "إدارة الأعمال المتكاملة",
    invoiceCount: "عدد الفواتير",
    averageInvoice: "متوسط قيمة الفاتورة",
    purchaseManagement: "إدارة المشتريات",

    // Additional Report Labels
    totalPurchases: "إجمالي المشتريات",
    supplierName: "اسم المورد",
    grossProfit: "الربح الإجمالي",
    netProfit: "صافي الربح",
    operatingExpenses: "المصاريف التشغيلية",
    profitMargin: "هامش الربح",
    costOfGoodsSold: "تكلفة البضاعة المباعة",
    profitAnalysis: "تحليل الأرباح والخسائر",
    inventoryManagement: "إدارة المخزون",
    stockValue: "قيمة المخزون",
    lowStock: "مخزون منخفض",
    categoryAnalysis: "تحليل الفئات",
    customerAnalysis: "تحليل العملاء",
    topCustomers: "أفضل العملاء",
    debtorsAnalysis: "تحليل المدينين",
    performanceAnalysis: "تحليل الأداء",
    dailyOperations: "العمليات اليومية",
    monthlyOperations: "العمليات الشهرية",
    yearlyOperations: "العمليات السنوية",
    kpiAnalysis: "تحليل مؤشرات الأداء",

    // Access Control
    accessRestricted: "الوصول مقيد",
    reportsManagerOnly: "التقارير والإحصائيات متاحة للمدير فقط",
    loginAsManager: "يرجى تسجيل الدخول بحساب المدير للوصول إلى هذه الصفحة",
    backToDashboard: "العودة للوحة التحكم",

    // Login
    login: "تسجيل الدخول",
    username: "اسم المستخدم",
    password: "كلمة المرور",
    welcomeBack: "مرحباً بعودتك",
    enterCredentials: "أدخل بيانات الدخول للوصول إلى النظام",

    // Month Names (Algerian Dialect)
    month1: "جانفي",
    month2: "فيفري",
    month3: "مارس",
    month4: "أفريل",
    month5: "ماي",
    month6: "جوان",
    month7: "جويلية",
    month8: "أوت",
    month9: "سبتمبر",
    month10: "أكتوبر",
    month11: "نوفمبر",
    month12: "ديسمبر",

    // Control Panel Tooltips
    disableSounds: "إيقاف الأصوات",
    enableSounds: "تفعيل الأصوات",
    keyboardShortcutsAlwaysActive: "الاختصارات مفعلة دائماً",
    disablePrinter: "إيقاف الطابعة",
    enablePrinter: "تفعيل الطابعة",

    // Dashboard Action Messages
    newPurchaseInvoiceClicked: "تم النقر على فاتورة مشتريات جديدة",
    purchaseReportClicked: "تم النقر على تقرير المشتريات",
    purchaseStatisticsClicked: "تم النقر على إحصائيات المشتريات",
    willOpenNewPurchaseInvoiceWindow: "📄 سيتم فتح نافذة إنشاء فاتورة مشتريات جديدة",
    willOpenDetailedPurchaseReport: "📊 سيتم فتح تقرير المشتريات المفصل",
    willOpenDetailedPurchaseStatistics: "📈 سيتم فتح إحصائيات المشتريات التفصيلية",

    // Print Functions
    invoiceNumberLabel: "فاتورة رقم:",
    dateLabel: "التاريخ:",
    customerLabel: "الزبون:",
    paymentMethodLabel: "طريقة الدفع:",
    productsLabel: "المنتجات",
    subtotalLabel: "المجموع الفرعي:",
    discountLabel: "الخصم:",
    taxLabel: "الضريبة",
    finalTotalLabel: "المجموع النهائي:",
    thankYouMessage: "شكراً لزيارتكم",
    printedAtLabel: "طُبعت في:",
    salesInvoiceTitle: "فاتورة مبيعات",
    invoiceInfo: "معلومات الفاتورة",
    customerInfo: "معلومات الزبون",
    customerName: "اسم الزبون",
    creationTime: "وقت الإنشاء",
    paymentInfo: "معلومات الدفع",
    invoiceStatus: "حالة الفاتورة",
    paidStatus: "مدفوعة",
    debtStatus: "دين",
    thankYouForDealingWithUs: "شكراً لتعاملكم معنا",
    allRightsReserved: "جميع الحقوق محفوظة",
    printWindowOpened: "تم فتح نافذة الطباعة",

    // Edit Invoice Modal
    editInvoiceTitle: "تعديل الفاتورة",
    invoiceItems: "عناصر الفاتورة",
    actions: "الإجراءات",
    saveChanges: "حفظ التعديلات",
    cancel: "إلغاء",
    insufficientStockForProduct: "المخزون غير كافي للمنتج",
    invoiceUpdatedAndStockAdjusted: "تم تحديث الفاتورة",
    andStockAdjusted: "وتحديث المخزون",
    notAllowedManagerOnlyEditInvoices: "غير مسموح - المدير فقط يمكنه تعديل الفواتير",

    // Credit Invoice Validation
    cannotSaveCreditForWalkInCustomer: "لا يمكن حفظ فاتورة دين لزبون عابر",
    pleaseSelectRegisteredCustomerForCredit: "يرجى اختيار زبون مسجل للدفع بالدين",

    // Sales Report Print
    additionalStatistics: "إحصائيات إضافية",
    totalTaxes: "إجمالي الضرائب",
    totalDiscounts: "إجمالي الخصومات",
    creditInvoicesCount: "فواتير الدين",
    creditInvoicesValue: "قيمة الديون",
    professionalSalesReportOpened: "تم فتح تقرير المبيعات الاحترافي للطباعة",

    // Additional Sales Report Keys
    salesSummary: "ملخص المبيعات",
    salesInvoiceDetails: "تفاصيل فواتير المبيعات",
    reportDate: "تاريخ التقرير",
    generationTime: "وقت الإنشاء",
    noSalesInvoicesToDisplay: "لا توجد فواتير مبيعات لعرضها",
    finalTotal: "المبلغ النهائي",
    lastInvoice: "آخر فاتورة"
  },

  fr: {
    // Language Selection
    selectLanguage: "Choisir la langue",
    arabic: "Arabe",
    french: "Français",
    english: "Anglais",
    languageSelected: "Langue sélectionnée avec succès",

    // Login & Authentication
    welcome: "Bienvenue",
    loginPrompt: "Connectez-vous pour accéder à votre compte",
    username: "Nom d'utilisateur",
    password: "Mot de passe",
    rememberMe: "Se souvenir de moi",
    login: "Se connecter",
    logout: "Déconnexion",
    systemName: "Système Comptable",
    allRightsReserved: "Tous droits réservés",
    version: "Version",
    systemDescription: "Système comptable intégré pour gérer votre entreprise",
    feature1: "Gestion des ventes et achats avec haute efficacité",
    feature2: "Gestion des stocks et surveillance des articles",
    feature3: "Rapports financiers et comptables intégrés",
    feature4: "Gestion facile des clients et fournisseurs",

    // User Management
    systemUser: "Utilisateur du système",
    systemManager: "Gestionnaire du système",
    seller: "Vendeur",
    manager: "Le Gestionnaire",

    // Navigation
    dashboard: "Tableau de bord",
    products: "Produits",
    sales: "Ventes",
    customers: "Clients",
    purchases: "Achats",
    reports: "Rapports",
    settings: "Paramètres",
    users: "Utilisateurs",

    // Dashboard
    systemOverview: "F6 📦 Ajouter un nouveau produit F7 📦 Nouvelle facture d'achat Enter 💾 Enregistrer la facture F3 ➕ Ajouter un produit ESC ❌ Fermer sans enregistrer",
    totalSales: "Total des ventes",
    totalInvoices: "Nombre de factures",
    totalProducts: "Total des produits",
    lowStockProducts: "Produits en stock faible",
    recentInvoicesOperations: "Dernières factures et opérations",
    quickOperations: "Opérations rapides",
    chooseOperation: "Choisissez l'opération que vous souhaitez effectuer",

    // Dashboard Stats
    invoice: "facture",
    invoices: "factures",
    product: "produit",
    products: "produits",

    // Table Headers
    invoiceNumber: "Numéro de facture",
    date: "Date",
    supplier: "Fournisseur",
    customer: "Client",
    amountPaid: "Montant payé",
    totalAmount: "Montant total",
    status: "Statut",

    // Recent Operations
    recentInvoicesAndOperations: "Dernières factures et opérations",

    // Status
    paid: "Payée",
    unpaid: "Impayée",
    debt: "Dette",

    // Customer Names
    walkInCustomer: "Client de passage",

    // Quick Actions
    quickSalesInvoice: "Facture de vente rapide",
    quickPurchaseInvoice: "Facture d'achat rapide",
    quickAddProduct: "Ajout rapide de produit",
    newSalesInvoice: "Nouvelle facture de vente",
    newPurchaseInvoice: "Nouvelle facture d'achat",
    purchaseReport: "Rapport des achats",
    purchaseStatistics: "Statistiques des achats",
    salesManagement: "Gestion des ventes",
    inventoryManagement: "Gestion des stocks",
    customerManagement: "Gestion des clients",

    // Dashboard KPIs and Performance
    performanceOverview: "Vue d'ensemble des performances",
    netProfit: "Bénéfice net",
    profitMargin: "Marge bénéficiaire",
    todaySales: "Ventes du jour",
    monthlySales: "Ventes du mois",
    monthlyGrowth: "Croissance mensuelle",
    allPeriods: "Toutes les périodes",
    growth: "Croissance",
    margin: "Marge",

    // Units and Currency
    dinar: "dinar",
    dinars: "dinars",
    unit: "unité",
    units: "unités",

    // Status
    paid: "Payée",
    unpaid: "Impayée",
    debt: "Dette",
    active: "Actif",
    inactive: "Inactif",

    // Customer Names
    walkInCustomer: "Client de passage",

    // Quick Actions
    quickSalesInvoice: "Facture de vente rapide",
    quickPurchaseInvoice: "Facture d'achat rapide",
    quickAddProduct: "Ajout rapide de produit",
    newSalesInvoice: "Nouvelle facture de vente",
    newPurchaseInvoice: "Nouvelle facture d'achat",
    purchaseReport: "Rapport des achats",
    purchaseStatistics: "Statistiques des achats",
    salesManagement: "Gestion des ventes",
    inventoryManagement: "Gestion des stocks",
    customerManagement: "Gestion des clients",
    totalCustomers: "Total des clients",
    lowStock: "Stock faible",
    recentSales: "Ventes récentes",
    topProducts: "Produits les plus vendus",

    // Sales
    newSalesInvoice: "Nouvelle facture de vente",
    newInvoice: "Nouvelle facture",
    report: "Rapport",
    totalSales: "Total des ventes",
    invoiceCount: "Nombre de factures",
    averageInvoice: "Facture moyenne",
    creditInvoices: "Factures à crédit",
    invoiceNumber: "Numéro de facture",
    date: "Date",
    customer: "Client",
    paymentMethod: "Mode de paiement",
    amount: "Montant",
    status: "Statut",
    actions: "Actions",
    noInvoicesSaved: "Aucune facture enregistrée",
    walkInCustomer: "Client de passage",
    cash: "Espèces",
    credit: "Crédit",
    paid: "Payée",
    debt: "Dette",
    viewInvoiceDetails: "Voir les détails de la facture",
    normalPrint: "Impression normale",
    thermalPrint: "Impression thermique",
    editInvoice: "Modifier la facture (Manager uniquement)",
    returnProducts: "Retourner des produits",
    deleteInvoice: "Supprimer la facture (Manager uniquement)",
    selectCustomer: "Sélectionner un client",
    scanBarcode: "Scanner le code-barres",
    product: "Produit",
    quantity: "Quantité",
    price: "Prix",
    total: "Total",
    add: "Ajouter",
    subtotal: "Sous-total",
    tax: "Taxe",
    discount: "Remise",
    finalTotal: "Total final",
    saveInvoice: "Enregistrer la facture",
    cancel: "Annuler",
    delete: "Supprimer",
    view: "Voir",
    print: "Imprimer",

    // Sales Invoice Modal
    saveInvoiceShortcut: "Enregistrer la facture",
    addProductShortcut: "Ajouter un produit",
    closeWithoutSaving: "Fermer sans enregistrer",
    scanBarcodeLabel: "Scanner le code-barres",
    scanBarcodePlaceholder: "Scannez le code-barres ou saisissez-le manuellement",
    searchProductPlaceholder: "Rechercher un produit (nom, code, code-barres)...",
    selectProduct: "Sélectionner un produit",
    available: "Disponible",
    invoiceItems: "Articles de la facture",
    noProductsAdded: "Aucun produit ajouté pour le moment",

    productName: "Produit",
    quantityRequiredExceedsStock: "La quantité demandée dépasse le stock disponible",
    deleteTitle: "Supprimer",
    selectRegisteredCustomer: "Sélectionner un client enregistré",
    action: "Action",
    activeBarcodeReader: "Lecteur de code-barres actif",

    // Purchase Management
    purchaseManagement: "Gestion des achats",
    newPurchaseInvoice: "Nouvelle facture d'achat",
    totalPurchases: "Total des achats",
    purchaseInvoiceCount: "Nombre de factures d'achat",
    averagePurchaseInvoice: "Facture d'achat moyenne",
    creditPurchaseInvoices: "Factures à crédit",
    supplier: "Fournisseur",
    noPurchaseInvoicesSaved: "Aucune facture d'achat enregistrée",
    generalSupplier: "Fournisseur général",
    editPurchaseInvoice: "Modifier la facture d'achat (Manager uniquement)",
    deletePurchaseInvoice: "Supprimer la facture d'achat (Manager uniquement)",

    // Purchase Invoice Modal
    editPurchaseInvoiceTitle: "Modifier la facture d'achat",
    savePurchaseInvoice: "Enregistrer la facture d'achat",
    updatePurchaseInvoice: "Mettre à jour la facture d'achat",
    purchaseInvoiceItems: "Articles de la facture d'achat",
    noPurchaseProductsAdded: "Aucun produit ajouté pour le moment",
    selectProductsFromList: "Sélectionnez les produits dans la liste ci-dessus",
    selectRegisteredSupplier: "Sélectionner un fournisseur enregistré",
    addNewSupplier: "+ Ajouter un nouveau fournisseur",
    purchasePrice: "Prix d'achat",
    addProductShortcut: "Ajouter un produit",
    closeWithoutSaving: "Fermer sans enregistrer",
    selectProduct: "Sélectionner un produit",
    available: "Disponible",
    addNewProduct: "Ajouter un nouveau produit",
    quantity: "Quantité",
    add: "Ajouter",
    productName: "Produit",
    total: "Total",
    action: "Action",
    delete: "Supprimer",
    discount: "Remise",
    subtotal: "Sous-total",
    tax: "Taxe",
    finalTotal: "Total final",
    cancel: "Annuler",

    // Customers
    customersManagement: "Gestion des clients",
    addNewCustomer: "Ajouter un nouveau client",
    customerName: "Nom du client",
    phone: "Téléphone",
    email: "E-mail",
    address: "Adresse",
    company: "Entreprise",
    balance: "Solde",
    creditLimit: "Limite de crédit",
    paymentTerm: "Délai de paiement",
    status: "Statut",
    active: "Actif",
    inactive: "Inactif",
    actions: "Actions",

    // Customer Management Page
    totalCustomers: "Total des clients",
    totalDues: "Total des créances",
    debtorCustomers: "Clients débiteurs",
    customerNumber: "Numéro client",
    profitMarginDiscount: "Remise (%)",
    editCustomer: "Modifier le client",
    deleteCustomer: "Supprimer le client",
    searchCustomers: "Rechercher dans les clients (nom, téléphone, email)...",
    searchSalesInvoices: "Rechercher dans les factures de vente (numéro de facture, nom du client)...",
    searchPurchaseInvoices: "Rechercher dans les factures d'achat (numéro de facture, nom du fournisseur)...",
    noCustomersMatchingFilter: "Aucun client ne correspond au filtre",
    noCustomersAdded: "Aucun client ajouté",
    noInvoicesMatchingFilter: "Aucune facture ne correspond au filtre",
    noPurchaseInvoicesMatchingFilter: "Aucune facture d'achat ne correspond au filtre",
    addNewCustomerTitle: "Ajouter un nouveau client",
    customerID: "Numéro du client",
    companyName: "Nom de l'entreprise",
    phoneNumber: "Numéro de téléphone",
    emailAddress: "Adresse e-mail",
    fullAddress: "Adresse complète",
    openingBalance: "Solde d'ouverture",
    creditLimitAmount: "Limite de crédit",
    paymentTermDays: "Délai de paiement (en jours)",
    save: "Enregistrer",
    cancel: "Annuler",
    customerSavedSuccess: "Données client enregistrées avec succès",

    // Customer Actions
    printCustomerData: "Imprimer les données client",
    thermalPrintCustomer: "Impression thermique client",
    viewCustomerOperations: "Voir les opérations client",
    confirmDeleteCustomer: "Êtes-vous sûr de supprimer ce client ?",
    yes: "Oui",
    no: "Non",
    customerDeletedSuccessfully: "Client supprimé avec succès",
    customerOperationsTitle: "Opérations du client",
    noOperationsFound: "Aucune opération trouvée pour ce client",
    customerReport: "Rapport client",

    // Payment functionality
    payCustomerDebt: "Payer les factures du client",
    paymentDetails: "Détails du paiement",
    paymentAmount: "Montant du paiement",
    maxPaymentAmount: "Montant maximum",
    currentDebt: "Dette actuelle",
    remainingBalance: "Solde restant",
    processPayment: "Confirmer le paiement",
    paymentProcessedSuccessfully: "Paiement traité avec succès",
    pleaseEnterValidAmount: "Veuillez entrer un montant valide",
    paymentExceedsBalance: "Le montant saisi dépasse le solde du client",
    for: "pour",

    // Customer Operations
    customerOperations: "Opérations du client",
    operationType: "Type d'opération",
    sale: "Vente",
    payment: "Paiement",
    paymentReceived: "Paiement reçu",
    totalSales: "Total des ventes",
    totalPayments: "Total des paiements",
    currentBalance: "Solde actuel",

    // Customer Operations with Payment
    customerOperationsWithPayment: "Opérations et paiements du client",
    paymentHistory: "Historique des paiements",
    makePayment: "Effectuer un paiement",
    paymentDate: "Date de paiement",
    paymentReference: "Référence de paiement",
    noPaymentsFound: "Aucun paiement trouvé pour ce client",
    totalPaid: "Total payé",
    outstandingBalance: "Solde en cours",
    lastPayment: "Dernier paiement",
    paymentSuccessful: "Paiement effectué avec succès",
    enterPaymentAmount: "Entrez le montant du paiement",
    paymentNotes: "Notes de paiement",

    // Print transactions
    customerTransactionsReport: "Rapport des transactions du client",
    transactionsAnalysis: "Analyse des transactions",
    transactionsSummary: "Résumé des transactions",
    totalTransactions: "Total des transactions",
    cashTransactions: "Transactions en espèces",
    creditTransactions: "Transactions à crédit",
    transactionsList: "Liste des transactions",
    transactionsReportOpened: "Rapport des transactions ouvert pour impression",
    thermalTransactionsReportOpened: "Rapport des transactions ouvert pour impression thermique",
    printA4: "Imprimer A4",
    printThermal: "Impression thermique",
    creditOperations: "Opérations à crédit",
    discountAppliedToProfit: "La remise est appliquée à la marge bénéficiaire et non au total des ventes",
    addNewCustomer: "Ajouter un nouveau client",

    // Products & Inventory Management
    productsManagement: "Gestion des produits",
    inventoryManagement: "Gestion des stocks",
    addNewProduct: "Ajouter un nouveau produit",
    newProduct: "Nouveau produit",
    productName: "Nom du produit",
    category: "Catégorie",
    barcode: "Code-barres",
    stock: "Stock",
    minStock: "Stock minimum",

    // Inventory Page Header
    dataManagement: "Données",
    exportExcel: "Exporter Excel",
    jsonBackup: "Sauvegarde JSON",
    importData: "Importer des données",
    deleteAllData: "Supprimer toutes les données",
    print: "Imprimer",

    // Search and Filters
    searchProducts: "Rechercher des produits (nom, code, code-barres)...",
    allCategories: "Toutes les catégories",
    allStatuses: "Tous les statuts",
    manageCategories: "Gérer les catégories",
    clearFilters: "Effacer les filtres",

    // Table Headers
    productCode: "Code produit",
    productName: "Nom du produit",
    barcode: "Code-barres",
    category: "Catégorie",
    buyPrice: "Prix d'achat",
    sellPrice: "Prix de vente",
    availableQuantity: "Quantité disponible",
    minStock: "Stock minimum",
    totalValue: "Valeur totale",
    status: "Statut",
    actions: "Actions",

    // Product Status
    normal: "Normal",
    high: "Élevé",
    low: "Faible",
    outOfStock: "Épuisé",
    notSpecified: "Non spécifié",

    // Table Messages
    noProductsFound: "Aucun produit ne correspond aux critères de recherche",
    noProductsInInventory: "Aucun produit en stock",
    clickToEdit: "Cliquer pour modifier",
    currentBarcode: "Code-barres actuel",
    shiftEnterSaveAndPrint: "Shift+Entrée: Facture enregistrée et envoyée à l'impression thermique",
    enterSaveOnly: "Entrée: Facture enregistrée",
    addProductByBarcode: "Ajouter un produit par code-barres",
    scanBarcodeToAddProduct: "Scanner le code-barres pour ajouter un produit à la facture",
    addProductFromList: "Ajouter un produit de la liste",
    selectProductOption: "Choisir un produit",

    // Summary Cards
    displayedProducts: "Produits affichés",
    totalProducts: "Total des produits",
    totalValue: "Valeur totale",
    lowStockProducts: "Produits en stock faible",
    outOfStockProducts: "Produits épuisés",
    outOfTotal: "sur",

    // Product Actions
    editProduct: "Modifier (Manager uniquement)",
    deleteProduct: "Supprimer (Manager uniquement)",
    requestStockUpdate: "Demander modification quantité (nécessite approbation manager)",
    managerOnly: "Manager uniquement",

    // Bulk Selection
    selectAll: "Tout sélectionner",
    selected: "sélectionné",
    deleteSelected: "Supprimer sélectionnés",
    pleaseSelectItems: "Veuillez sélectionner des éléments à supprimer",
    confirmDeleteSelected: "Êtes-vous sûr de vouloir supprimer les éléments sélectionnés ?",
    selectedItemsDeleted: "Éléments sélectionnés supprimés",
    andStockRestored: "et produits retournés au stock",
    andStockAdjusted: "et stock ajusté",
    stockWillBeAdjusted: "Le stock sera ajusté automatiquement",
    allProductsWillBeRestored: "Tous les produits seront retournés au stock",
    notAllowedManagerOnlyBulkDelete: "Non autorisé - Seul le manager peut effectuer une suppression multiple",

    // Product Modal
    editExistingProduct: "Modifier un produit existant",
    addNewProduct: "Ajouter un nouveau produit",
    scanBarcode: "Scanner le code-barres",
    scanBarcodeOrEnter: "Scannez le code-barres ou saisissez-le manuellement - génération automatique si laissé vide",
    generateAutoBarcode: "Générer un code-barres automatique",
    clearBarcode: "Effacer le code-barres",
    barcodeHelp: "Vous pouvez scanner le code-barres avec un lecteur ou le saisir manuellement. Si le code-barres existe en stock, les informations du produit s'afficheront pour modification. Si laissé vide, un code-barres sera généré automatiquement lors de la sauvegarde.",
    productNumber: "Numéro du produit",
    enterProductName: "Saisir le nom du produit",
    selectCategory: "Sélectionner la catégorie",
    currentBarcode: "📷 Code-barres actuel",
    barcodeWillShow: "Le code-barres s'affichera ici",
    buyPrice: "Prix d'achat",
    sellPrice: "Prix de vente",
    currentQuantity: "Quantité actuelle",
    minStock: "Stock minimum",
    saveChanges: "Enregistrer les modifications",
    saveProduct: "Enregistrer le produit",
    cancel: "Annuler",

    // Category Management Modal
    categoryManagement: "Gestion des catégories de produits",
    addNewCategory: "Ajouter une nouvelle catégorie",
    newCategoryName: "Nom de la nouvelle catégorie",
    add: "Ajouter",
    existingCategories: "Catégories existantes",
    edit: "Modifier",
    delete: "Supprimer",

    // Settings
    settings: "Paramètres",
    storeAndSellersManagement: "Gestion des paramètres du magasin et des vendeurs",
    storeSettings: "Paramètres du magasin",
    editSettings: "Modifier les paramètres",
    storeName: "Nom du magasin",
    phoneNumber: "Numéro de téléphone",
    storePhone: "Téléphone du magasin",
    storeAddress: "Adresse du magasin",
    address: "Adresse",
    currency: "Devise",
    taxRate: "Taux de taxe",
    language: "Langue",
    theme: "Thème",
    lightMode: "Mode clair",
    darkMode: "Mode sombre",
    save: "Enregistrer",
    saveSettings: "Enregistrer les paramètres",

    // Seller Management
    sellersManagement: "Gestion des vendeurs",
    addNewSeller: "Ajouter un nouveau vendeur",
    sellerNumber: "Numéro",
    sellerName: "Nom",
    username: "Nom d'utilisateur",
    phone: "Téléphone",
    role: "Rôle",
    status: "Statut",
    creationDate: "Date de création",
    actions: "Actions",
    admin: "Administrateur",
    seller: "Vendeur",
    active: "Actif",
    inactive: "Inactif",
    activate: "Activer",
    deactivate: "Désactiver",
    delete: "Supprimer",

    // Seller Modal
    addNewSellerTitle: "Ajouter un nouveau vendeur",
    sellerID: "ID du vendeur",
    sellerNameLabel: "Nom du vendeur",
    usernameLabel: "Nom d'utilisateur",
    passwordLabel: "Mot de passe",
    phoneLabel: "Numéro de téléphone",
    emailLabel: "Adresse e-mail",
    roleLabel: "Rôle",
    enterSellerName: "Entrez le nom du vendeur",
    enterUsername: "Entrez le nom d'utilisateur",
    enterPassword: "Entrez le mot de passe",
    enterPhone: "Entrez le numéro de téléphone",
    enterEmail: "Entrez l'adresse e-mail",
    selectRole: "Sélectionnez le rôle",

    // Store Settings Modal
    storeSettingsModal: "Paramètres du magasin",
    storeNameRequired: "Nom du magasin",
    enterStoreName: "Entrez le nom du magasin",
    storeNumberLabel: "Numéro du magasin",
    storeNumberPlaceholder: "ST001",
    phoneNumberLabel: "Numéro de téléphone",
    phoneNumberPlaceholder: "+*********** 456",
    addressLabel: "Adresse",
    fullStoreAddress: "Adresse complète du magasin",
    taxRateLabel: "Taux de taxe (%)",
    taxRatePlaceholder: "19",
    currencyLabel: "Devise",
    algerianDinar: "Dinar algérien (DZD)",
    usDollar: "Dollar américain (USD)",
    euro: "Euro (EUR)",
    adminPasscodeLabel: "Code d'Administration",
    adminPasscodePlaceholder: "010290",
    adminPasscodeHelp: "Code requis pour supprimer et modifier les données sensibles",
    storeLogo: "Logo du magasin",
    logoPreview: "Logo du magasin",

    // Edit Customer Modal
    editCustomerData: "Modifier les données du client",
    customerID: "ID du client",
    customerNameRequired: "Nom du client",
    enterCustomerName: "Entrez le nom du client",
    phoneNumberPlaceholder: "Numéro de téléphone",
    emailPlaceholder: "Adresse e-mail",
    addressPlaceholder: "Adresse",
    companyPlaceholder: "Nom de l'entreprise",
    openingBalance: "Solde d'ouverture",
    creditLimit: "Limite de crédit",
    paymentTermDays: "Délai de paiement (en jours)",
    profitMarginDiscount: "Remise (%)",
    discountPercentagePlaceholder: "Pourcentage de remise sur marge bénéficiaire",
    discountAppliedToProfit: "La remise est appliquée à la marge bénéficiaire, pas au total des ventes",
    saveChanges: "Enregistrer les modifications",

    // Expenses Modal
    editExpense: "Modifier la dépense",
    addNewExpense: "Ajouter une nouvelle dépense",
    date: "Date",
    category: "Catégorie",
    amount: "Montant",
    paymentMethod: "Mode de paiement",
    description: "Description",
    expenseDescription: "Entrez une description détaillée de la dépense",
    salariesWages: "Salaires et traitements",
    rent: "Loyer",
    utilities: "Services publics (électricité, eau, internet)",
    taxesFees: "Taxes et frais",
    marketingAdvertising: "Marketing et publicité",
    maintenanceRepairs: "Maintenance et réparations",
    transportationTravel: "Transport et déplacements",
    otherExpenses: "Autres dépenses",

    // Repair Management Translations
    repairManagement: "Gestion des Réparations",
    repairs: "Réparations",
    newBonPour: "Nouveau Bon Pour",
    repairCompleted: "Réparation Terminée",
    clientPickup: "Récupération Client",
    repairOrder: "Ordre de Réparation",
    createRepairOrder: "Créer Ordre de Réparation",

    // Form Fields
    clientDeviceInfo: "Informations Client et Appareil",
    clientFullName: "Nom Complet du Client",
    problemTypeInfo: "Type de Problème et Description",
    pricingPaymentInfo: "Prix et Paiement",
    dateDetailsInfo: "Date et Détails",
    deviceName: "Nom de l'Appareil",
    deviceType: "Type d'Appareil",
    problemDescription: "Description du Problème",
    repairPrice: "Prix de Réparation",
    partialPayment: "Paiement Partiel",
    paymentStatus: "Statut de Paiement",
    depositDate: "Date de Dépôt",
    depositTime: "Heure de Dépôt",
    repairBarcode: "Code-barres de Réparation",
    remarks: "Remarques",

    // Device Types
    smartphone: "Smartphone",
    tablet: "Tablette",
    pc: "PC",
    console: "Console",

    // Problem Types
    lcd: "LCD",
    lcdWithFrame: "LCD Avec Cadre",
    chargingPort: "Port de Charge",
    glass: "Verre",
    backGlass: "Verre Arrière",
    battery: "Batterie",
    frontCamera: "Caméra Avant",
    rearCamera: "Caméra Arrière",
    microphone: "Microphone",
    loudspeaker: "Haut-parleur",
    earpieceSpeaker: "Haut-parleur Écouteur",
    waterDamage: "Dégât des Eaux",
    powerButton: "Bouton d'Alimentation",
    volumeButtons: "Boutons de Volume",
    homeButton: "Bouton Accueil",
    slowPerformance: "Performance Lente",
    androidCorruption: "Corruption Android",
    osCorruption: "Corruption OS",
    wifiIssues: "Problèmes Wi-Fi",
    bluetoothIssues: "Problèmes Bluetooth",
    cellularNetworkIssues: "Problèmes Réseau Cellulaire",
    noSound: "Pas de Son",
    headphoneJackIssues: "Problèmes Prise Casque",
    vibratorMotorFailure: "Panne Moteur Vibreur",
    proximitySensor: "Capteur de Proximité",
    gyroscope: "Gyroscope",
    fingerprintSensor: "Capteur d'Empreinte",
    overheating: "Surchauffe",
    storageFull: "Stockage Plein",
    backlightIssues: "Problèmes Rétroéclairage",
    housingDamage: "Dommage Boîtier",
    addNewProblem: "Ajouter Nouveau Problème",

    // Payment Status
    paid: "Payé",
    partiallyPaid: "Partiellement Payé",
    unpaid: "Non Payé",

    // Repair Status
    inProcess: "En Cours",
    waitingForClient: "En Attente du Client",
    notSuccess: "Non Réussi",
    done: "Terminé",

    // Actions
    createAndPrint: "Créer et Imprimer",
    cancel: "Annuler",
    viewInfo: "Voir Infos",
    edit: "Modifier",
    printQRCode: "Imprimer Code QR",
    printBonPour: "Imprimer Bon Pour",

    // Process Messages
    repairCompletedSuccess: "Réparation terminée avec succès",
    repairCompletedFailure: "Échec de la réparation",
    partsChangedPrice: "Prix des Pièces Changées",
    clientPickupCompleted: "Récupération client terminée",

    // Table Headers
    clientName: "Nom Client",
    deviceNameHeader: "Nom Appareil",
    problem: "Problème",
    repairPriceHeader: "Prix Réparation",
    partsPrice: "Prix Pièces",
    interestRate: "Taux d'Intérêt",
    situation: "Situation",
    actions: "Actions",

    // Admin Protection
    adminPasscodeRequired: "Code Admin Requis",
    enterAdminPasscode: "Entrer Code Admin",
    enterAdminPasscodeToEdit: "Entrez le code administrateur pour modifier la réparation",
    incorrectPasscode: "Code Incorrect",
    editRepair: "Modifier la Réparation",
    deleteRepair: "Supprimer la Réparation",
    enterPasscode: "Entrez le mot de passe",

    // QR Scanner
    scanQRCode: "Scanner Code QR",
    qrScannerActive: "Scanner QR Actif",
    qrCodeScanned: "Code QR Scanné",
    qrCode: "Code QR",
    repairQRCode: "Code QR de Réparation",
    repairDetails: "Détails de Réparation",
    clickToEnlarge: "Cliquer pour Agrandir",
    qrGenerationError: "Erreur de Génération QR",
    printTicket: "Imprimer Ticket",
    totalPrice: "Prix Total",
    repairCompletionQuestion: "La réparation a-t-elle réussi?",
    didRepairSucceed: "La réparation a-t-elle réussi?",
    repairSuccess: "Réparation Réussie",
    repairNotSuccess: "Réparation Échouée",
    supplierName: "Nom du Fournisseur",
    selectSupplier: "Sélectionner Fournisseur",
    printingQRCode: "Impression Code QR",
    qrCodePrinted: "Code QR Imprimé",
    printingError: "Erreur d'Impression",
    scanQRForRecovery: "Scanner le Code QR pour les informations automatiques du client",
    scanForAutoFill: "Scanner pour remplir automatiquement les informations client et téléphone",
    manualSearchPlaceholder: "Ou rechercher manuellement par nom de client...",
    findRepairToRecover: "Trouver la Réparation à Récupérer",
    scanOrSelectRepair: "Scanner le QR code ou sélectionner manuellement",
    qrCodeScanner: "Scanner QR Code",
    quickestMethod: "Méthode la plus rapide",
    manualSearch: "Recherche Manuelle",
    searchByName: "Rechercher par nom",
    selectFromList: "Sélectionner dans la Liste",
    browseAllReady: "Parcourir toutes les réparations prêtes",
    scanCodeBarre: "Scanner Code Barre",
    clientInformation: "Informations Client",
    phoneNumber: "Numéro de Téléphone",
    priceBreakdown: "Détail des Prix",
    saveAndComplete: "Sauvegarder et Terminer",
    optionalPrintActions: "Optionnel: Appuyez sur Shift + Entrée pour:",
    completeRecovery: "Terminer la Récupération",
    printFinalInvoice: "Imprimer Facture Finale",

    // Printing Messages
    printingRepairTicket: "Impression Ticket Réparation",
    printingQRCode: "Impression Code QR",
    repairTicketPrinted: "Ticket Réparation Imprimé",
    qrCodePrinted: "Code QR Imprimé",

    // Enhanced Thermal Printing Messages
    thermalPrintButton: "Impression Thermique",
    printThermal: "Impression Thermique",
    repairOrdersList: "Liste des Ordres de Réparation",
    repairOrdersListPrinted: "Liste des Ordres de Réparation Imprimée",
    supplierTransactions: "Transactions Fournisseur",
    supplierInformation: "Informations Fournisseur",
    transactionsList: "Liste des Transactions",
    transactionCount: "Nombre de Transactions",
    andMore: "et plus",
    clientPickupReceipt: "Reçu de Récupération Client",
    repairSummary: "Résumé de Réparation",
    repairStatus: "Statut de Réparation",
    successful: "Réussi",
    notSuccessful: "Non Réussi",
    paymentDetails: "Détails de Paiement",
    finalAmount: "Montant Final",
    pickupTime: "Heure de Récupération",
    thankYouForTrust: "Merci pour votre confiance",
    summary: "Résumé",
    totalOrders: "Total des Ordres",
    printDate: "Date d'Impression",
    totals: "Totaux",
    totalValue: "Valeur Totale",
    reportGenerated: "Rapport Généré",
    repairReceipt: "Reçu de Réparation",
    repairInformation: "Informations de Réparation",
    repairId: "ID de Réparation",
    pricingDetails: "Détails des Prix",

    // New Thermal Printing Translations
    repairTicket: "Ticket de Réparation",
    repairSystem: "Système de Réparation - ICALDZ",
    ticketNumber: "Numéro de Ticket",
    printedOn: "Imprimé le",
    clientInvoice: "Facture Client",
    invoiceNumber: "Numéro de Facture",
    time: "Heure",
    paymentMode: "Mode de Paiement",
    cash: "Espèces",
    service: "Service",
    qty: "Qté",
    price: "Prix",
    total: "Total",
    repairParts: "Pièces de Réparation",
    subtotal: "Sous-total",
    tax: "Taxe",
    finalTotal: "Total Final",
    thankYouVisit: "Merci de votre visite",

    // Paste Ticket Translations
    printingPasteTicket: "Impression Ticket Collage",
    pasteTicketPrinted: "Ticket Collage Imprimé",
    printPasteTicket: "Imprimer Ticket Collage",
    noDescription: "Sans description",
    interestRate: "Taux d'intérêt",

    // Supplier Transaction Translations
    supplierTransactions: "Transactions Fournisseur",
    supplierName: "Nom du Fournisseur",
    totalTransactions: "Total des Transactions",
    description: "Description",
    partProblem: "Problème de Pièce",
    partsPrice: "Prix des Pièces",
    date: "Date",
    amount: "Montant",
    status: "Statut",
    paid: "Payé",
    pending: "En attente",
    paidAmount: "Montant Payé",
    remainingCredit: "Crédit Restant",
    thankYouBusiness: "Merci pour votre collaboration",

    // Additional Repair Translations
    manageRepairOrders: "Gérer les ordres de réparation et suivre l'état des appareils",
    createNewRepairOrder: "Créer un nouvel ordre de réparation",
    markRepairAsCompleted: "Marquer la réparation comme terminée",
    processClientPickup: "Traiter la récupération client",
    repairOrders: "Ordres de Réparation",
    searchRepairs: "Rechercher dans les réparations...",
    allStatuses: "Tous les Statuts",
    noRepairsFound: "Aucune réparation trouvée",
    pleaseEnterClientName: "Veuillez entrer le nom du client",
    pleaseEnterDeviceName: "Veuillez entrer le nom de l'appareil",
    pleaseSelectDeviceType: "Veuillez sélectionner le type d'appareil",
    pleaseSelectProblemType: "Veuillez sélectionner le type de problème",
    pleaseEnterValidRepairPrice: "Veuillez entrer un prix de réparation valide",
    repairOrderCreated: "Ordre de réparation créé avec succès",
    canOnlyEditCompletedRepairs: "Seules les réparations terminées peuvent être modifiées",
    repairMarkedAsCompleted: "Réparation marquée comme terminée",
    repairMarkedAsFailed: "Réparation marquée comme échouée",
    remarksRequiredForFailure: "Remarques requises en cas d'échec",
    repairNotReadyForPickup: "Réparation pas prête pour récupération",
    repairNotFound: "Réparation non trouvée",
    invalidQRCode: "Code QR invalide",
    repairFound: "Réparation trouvée",
    enterClientName: "Entrer le nom du client",
    enterDeviceName: "Entrer le nom de l'appareil",
    selectDeviceType: "Sélectionner le type d'appareil",
    selectProblem: "Sélectionner le problème",
    enterNewProblem: "Entrer le nouveau problème",
    enterRemarks: "Entrer des remarques supplémentaires",
    autoGenerated: "Généré automatiquement",
    optional: "Optionnel",
    explainFailureReason: "Expliquer la raison de l'échec",
    markAsWaitingForClient: "Marquer comme en attente du client",
    markAsNotSuccess: "Marquer comme non réussi",
    scanClientQRCode: "Scanner le code QR du client pour afficher les informations de réparation",
    openQRScanner: "Ouvrir le scanner QR",
    repairSummary: "Résumé de Réparation",
    totalAmount: "Montant Total",
    completePickup: "Terminer la Récupération",
    printFinalInvoice: "Imprimer la Facture Finale",
    pointCameraAtQR: "Pointer la caméra vers le code QR",
    orEnterManually: "Ou entrer manuellement",
    enterRepairCode: "Entrer le code de réparation",
    search: "Rechercher",
    repairCompletionInfo: "Choisir une réparation en cours pour la marquer comme terminée",
    noInProgressRepairs: "Aucune réparation en cours actuellement",
    createNewRepairFirst: "Créer d'abord une nouvelle réparation ou vérifier les statuts de réparation",
    chooseRepairOutcome: "Choisir le résultat de la réparation pour mettre à jour le statut en conséquence",
    selectSuccessOrFailure: "Sélectionner si la réparation a réussi ou échoué",
    selectRepairToComplete: "Sélectionner la réparation à finaliser",
    chooseInProgressRepair: "Scanner le QR code ou sélectionner manuellement",
    repairSuccessDescription: "Réparation terminée avec succès - sera marquée comme 'En attente du client'",
    repairNotSuccessDescription: "Réparation non terminée - sera marquée comme 'Échec'",
    verificationPriceNote: "Prix facturé pour le diagnostic/vérification",
    failureReason: "Raison de l'échec",
    reviewAndComplete: "Réviser et Finaliser",
    confirmDetailsAndComplete: "Vérifier les détails et finaliser la récupération",
    finalPrice: "Prix Final (si différent)",
    finalPriceNote: "Laisser vide pour utiliser le prix calculé",
    selectRepairFromList: "Choisir une réparation de la liste...",
    orSearchManually: "Ou Rechercher Manuellement",
    searchByNamePhoneDevice: "Rechercher par nom, téléphone ou appareil...",
    searchByNamePhoneDate: "Rechercher par nom, téléphone ou date...",
    pricingBreakdown: "Détail des Prix",
    noRepairsReady: "Aucune réparation prête pour récupération",
    checkRepairStatuses: "Vérifier les statuts de réparation ou terminer les réparations d'abord",
    back: "Retour",
    terminerRecuperation: "Terminer la Récupération",
    printingFinalInvoice: "Impression de la facture finale",
    finalInvoice: "Facture Finale",
    completionDate: "Date d'achèvement",
    repairCompleted: "Réparation terminée avec succès",
    finalInvoicePrinted: "Facture finale imprimée",

    // Info Modal Translations
    repairInformation: "Informations de Réparation",
    printA4: "Imprimer A4",
    clientInformation: "Informations Client",
    deviceInformation: "Informations Appareil",
    pricingInformation: "Informations Tarification",
    statusInformation: "Informations Statut",
    currentStatus: "Statut Actuel",
    completionRemarks: "Remarques de Finalisation",
    failureRemarks: "Remarques d'Échec",
    timestamps: "Horodatages",
    createdAt: "Créé le",
    completedAt: "Terminé le",
    pickedUpAt: "Récupéré le",
    printingError: "Erreur d'impression",
    thankYou: "Merci de votre confiance",
    cash: "Espèces",
    creditCard: "Carte de crédit",
    bankTransfer: "Virement bancaire",
    check: "Chèque",
    otherPaymentMethod: "Autre méthode",

    // Supplier Modal
    addNewSupplier: "Ajouter un nouveau fournisseur",
    supplierNumber: "Numéro du fournisseur",
    supplierName: "Nom du fournisseur",
    enterSupplierName: "Entrez le nom du fournisseur",
    supplierPhone: "Numéro de téléphone",
    supplierEmail: "Adresse e-mail",
    supplierAddress: "Adresse",
    fullAddress: "Adresse complète",
    saveSupplier: "Enregistrer le fournisseur",

    // Supplier Management Modal
    suppliersManagement: "Gestion des fournisseurs",
    addNewSupplierButton: "Ajouter un nouveau fournisseur",
    supplierID: "ID du fournisseur",
    supplierNameHeader: "Nom du fournisseur",
    supplierPhoneHeader: "Numéro de téléphone",
    supplierEmailHeader: "Adresse e-mail",
    supplierAddressHeader: "Adresse",
    noSuppliersAdded: "Aucun fournisseur ajouté",
    deleteSupplier: "Supprimer",
    editSupplier: "Modifier le fournisseur",
    confirmDeleteSupplier: "Êtes-vous sûr de vouloir supprimer ce fournisseur ?",
    supplierDeletedSuccessfully: "Fournisseur supprimé avec succès",
    supplierUpdatedSuccessfully: "Fournisseur mis à jour avec succès",
    pleaseEnterSupplierName: "Veuillez entrer le nom du fournisseur",
    enterSupplierPhone: "Entrez le numéro de téléphone",
    enterSupplierEmail: "Entrez l'adresse e-mail",
    enterSupplierAddress: "Entrez l'adresse",
    close: "Fermer",

    // Supplier Management Modal
    suppliersManagement: "Gestion des fournisseurs",
    addNewSupplierButton: "Ajouter un nouveau fournisseur",
    supplierID: "ID du fournisseur",
    supplierNameHeader: "Nom du fournisseur",
    supplierPhoneHeader: "Numéro de téléphone",
    supplierEmailHeader: "Adresse e-mail",
    supplierAddressHeader: "Adresse",
    noSuppliersAdded: "Aucun fournisseur ajouté",
    deleteSupplier: "Supprimer",
    close: "Fermer",

    // Messages
    fillRequiredFields: "Veuillez remplir tous les champs obligatoires",
    usernameExists: "Le nom d'utilisateur existe déjà",
    sellerAddedSuccess: "Vendeur ajouté avec succès !",
    confirmDeleteSeller: "Êtes-vous sûr de vouloir supprimer ce vendeur ?",
    sellerDeletedSuccess: "Vendeur supprimé avec succès",
    sellerStatusUpdated: "Statut du vendeur mis à jour",
    storeSettingsSaved: "Paramètres du magasin enregistrés avec succès",
    expenseUpdatedSuccess: "Dépense mise à jour avec succès",
    expenseAddedSuccess: "Dépense ajoutée avec succès",

    // Edit Product Modal
    editProduct: "Modifier le produit",
    productNumber: "Numéro du produit",
    productName: "Nom du produit",
    enterProductName: "Entrez le nom du produit",
    selectCategory: "Sélectionnez la catégorie",
    categoryManagement: "Gestion des catégories",
    barcode: "Code-barres",
    buyPrice: "Prix d'achat",
    sellPrice: "Prix de vente",
    currentStock: "Stock actuel",
    minStock: "Stock minimum",
    saveChanges: "Enregistrer les modifications",

    // New Product in Purchase Modal
    addNewProduct: "Ajouter un nouveau produit",
    purchasedQuantity: "Quantité achetée",
    addProduct: "Ajouter le produit",
    enterBuyPrice: "Entrez le prix d'achat",
    enterSellPrice: "Entrez le prix de vente",
    enterInitialStock: "Entrez la quantité initiale",
    enterMinStock: "Entrez le stock minimum",
    initialStock: "Quantité initiale",
    redirectToInventory: "Redirection vers la gestion des stocks pour ajouter un nouveau produit",
    addNewProductOpened: "Fenêtre d'ajout de nouveau produit ouverte",

    // Edit Invoice Modal
    editInvoice: "Modifier la facture",
    invoiceNumber: "Numéro de facture",
    customer: "Client",
    invoiceItems: "Articles de la facture",
    product: "Produit",
    price: "Prix",
    quantity: "Quantité",
    total: "Total",
    subtotal: "Sous-total",
    tax: "Taxe",
    finalTotal: "Total final",

    // Return Products Modal
    returnProducts: "Retourner des produits de la facture",
    invoiceDate: "Date de la facture",
    originalTotal: "Total original",
    selectProductsToReturn: "Sélectionner les produits à retourner",
    originalQuantity: "Quantité originale",
    returnQuantity: "Quantité de retour",
    returnValue: "Valeur de retour",
    totalReturnValue: "Valeur totale de retour",
    confirmReturn: "Confirmer le retour",

    // Sales Invoice Modal - Additional Keys
    saveInvoiceShortcut: "Enregistrer la facture",
    addProductShortcut: "Ajouter un produit",
    closeWithoutSaving: "Fermer sans enregistrer",
    selectRegisteredCustomer: "Sélectionner un client enregistré",

    searchProductPlaceholder: "Rechercher un produit (nom, code, code-barres)...",
    selectProduct: "Sélectionner un produit",
    available: "Disponible",
    noProductsAdded: "Aucun produit ajouté pour le moment",

    invoiceItems: "Articles de la facture",
    productName: "Produit",
    action: "Action",
    deleteTitle: "Supprimer",
    finalTotal: "Total final",
    saveInvoice: "Enregistrer la facture",
    quantityRequiredExceedsStock: "La quantité demandée dépasse le stock disponible",

    // Sales Invoice Barcode Scanner
    salesBarcodeScanner: "Scanner de code-barres pour les ventes",
    scanToAddProduct: "Scanner le code-barres pour ajouter un produit",
    barcodeActive: "Scanner de code-barres actif",
    clearBarcode: "Effacer le code-barres",
    productDisplay: "Affichage du produit",
    waitingForScan: "En attente du scan du code-barres...",
    activeBarcodeScanner: "Actif - Scanner le code-barres",
    clear: "Effacer",

    // Sales Invoice Popup Notifications
    pleaseAddProductsToInvoice: "Veuillez ajouter des produits à la facture",
    productNotFoundInSystem: "Produit introuvable",
    quantityExceedsStock: "La quantité demandée dépasse le stock disponible",
    salesInvoiceSavedSuccessfully: "Facture de vente numéro",
    successfullyAndStockUpdated: "enregistrée avec succès et stock mis à jour!",
    invoiceSentToThermalPrinter: "Facture envoyée à l'imprimante thermique",
    discountApplied: "Remise appliquée de",
    fromProfitMarginForCustomer: "sur la marge bénéficiaire pour le client",
    forWalkInCustomer: "pour client de passage",
    forCustomer: "pour le client",
    invoicePrintedAutomatically: "Facture imprimée automatiquement",

    // Keyboard Shortcuts Notifications
    f1NewSalesInvoiceOpened: "Nouvelle facture de vente ouverte",
    f2NewInvoiceOpened: "Nouvelle facture ouverte",
    f3ProductAdded: "Produit ajouté",
    f3ProductAddedToPurchase: "Produit ajouté aux achats",
    f3OnlyAvailableInInvoice: "Disponible uniquement dans la fenêtre de facture",
    f6NewProductOpened: "Fenêtre d'ajout de nouveau produit ouverte",
    f6NavigatedToInventory: "Navigation vers gestion des stocks et ouverture fenêtre nouveau produit",
    f7NewPurchaseInvoiceOpened: "Nouvelle facture d'achat ouverte",
    f7NavigatedToPurchases: "Navigation vers gestion des achats et ouverture nouvelle facture d'achat",

    // Modal Shortcuts
    saveInvoiceShortcut: "Enregistrer la facture",
    addProductShortcut: "Ajouter un produit",
    closeWithoutSaving: "Fermer sans enregistrer",
    savePurchaseInvoice: "Enregistrer la facture d'achat",

    // Product Management Notifications
    productAddedToInvoice: "Ajouté",
    toInvoice: "à la facture",
    productUpdatedSuccessfully: "Produit mis à jour",
    successfully: "avec succès",
    productAddedWithScannedBarcode: "Produit ajouté",
    withScannedBarcode: "avec code-barres scanné:",
    productAddedWithAutoBarcode: "Produit ajouté",
    withAutoBarcode: "avec code-barres automatique:",
    confirmDeleteProduct: "Êtes-vous sûr de vouloir supprimer le produit",
    productDeletedSuccessfully: "Produit supprimé",

    // Supplier Management Notifications
    pleaseEnterSupplierName: "Veuillez saisir le nom du fournisseur",
    supplierAddedSuccessfully: "Fournisseur ajouté avec succès",
    confirmDeleteSupplier: "Êtes-vous sûr de vouloir supprimer ce fournisseur?",
    supplierDeletedSuccessfully: "Fournisseur supprimé avec succès",

    // System Control Notifications
    soundEnabled: "Sons activés",
    soundDisabled: "Sons désactivés",
    keyboardShortcutsInfo: "Raccourcis actifs: F1 facture vente, F2 nouvelle facture, F3 ajouter, F6 nouveau produit, F7 facture achat, Entrée sauvegarder, ESC fermer",
    keyboardShortcutsEnabled: "Raccourcis clavier activés",
    keyboardShortcutsDisabled: "Raccourcis clavier désactivés",
    enableKeyboardShortcuts: "Activer les raccourcis clavier",
    disableKeyboardShortcuts: "Désactiver les raccourcis clavier",
    printerEnabled: "Imprimante activée",
    printerDisabled: "Imprimante désactivée",
    notificationsEnabled: "Notifications activées",
    notificationsDisabled: "Notifications désactivées",
    enableNotifications: "Activer les notifications",
    disableNotifications: "Désactiver les notifications",
    addProduct: "Ajouter un produit",
    scrollToTop: "Retour en haut",
    saveInvoice: "Enregistrer la facture",

    // Credit Limit Validation
    creditLimitExceeded: "Limite de crédit dépassée",
    currentBalance: "Solde actuel",
    invoiceAmount: "Montant de la facture",
    newBalance: "Nouveau solde",
    exceededAmount: "Montant dépassé",

    // Bulk Selection
    selectAll: "Tout sélectionner",
    deselectAll: "Tout désélectionner",
    deleteSelected: "Supprimer la sélection",
    pleaseSelectItems: "Veuillez sélectionner des éléments à supprimer",
    confirmDeleteSelected: "Êtes-vous sûr de vouloir supprimer les éléments sélectionnés ?",
    selectedItemsDeleted: "Éléments sélectionnés supprimés",
    itemsSelected: "éléments sélectionnés",
    notAllowedManagerOnlyBulkDelete: "Non autorisé - Seul le manager peut effectuer la suppression en lot",
    andStockRestored: "et produits retournés au stock",
    stockWillBeAdjusted: "Le stock sera ajusté automatiquement",
    andStockAdjusted: "et stock ajusté",
    notAllowedManagerOnlyBulkDelete: "Non autorisé - Seul le manager peut effectuer la suppression en lot",
    andStockRestored: "et produits retournés au stock",
    stockWillBeAdjusted: "Le stock sera ajusté automatiquement",
    andStockAdjusted: "et stock ajusté",

    // Invoice Management Notifications
    invoiceDeletedAndStockRestored: "Facture supprimée",
    andStockRestored: "et produits retournés au stock",
    notAllowedManagerOnly: "Non autorisé - Seul le manager peut modifier le stock",
    stockQuantityUpdated: "Quantité en stock mise à jour avec succès",
    productRemovedFromInvoice: "Supprimé",
    fromInvoice: "de la facture",
    productRemovedFromPurchase: "Produit supprimé de la facture",
    confirmDeleteInvoice: "Êtes-vous sûr de vouloir supprimer la facture numéro",
    allProductsWillBeRestored: "Tous les produits seront retournés au stock.",
    confirmDeletePurchaseInvoice: "Êtes-vous sûr de vouloir supprimer la facture d'achat",
    purchaseInvoiceDeletedAndStockAdjusted: "Facture d'achat supprimée et stock réajusté",
    invoiceUpdatedAndStockAdjusted: "Facture mise à jour",
    andStockAdjusted: "et stock mis à jour",
    confirmDeleteAllData: "Êtes-vous sûr de vouloir supprimer toutes les données? Cette action ne peut pas être annulée!",
    allInventoryDataDeleted: "Toutes les données d'inventaire supprimées",
    reportSentToPrinter: "Rapport envoyé à l'imprimante",

    // Purchase Management Notifications
    pleaseSelectProduct: "Veuillez sélectionner un produit",
    pleaseEnterValidQuantity: "Veuillez saisir une quantité valide",
    pleaseEnterValidPrice: "Veuillez saisir un prix valide",
    pleaseSelectSupplier: "Veuillez sélectionner un fournisseur",
    purchaseInvoiceOpenedForEdit: "Facture d'achat ouverte pour modification",
    notAllowedManagerOnlyPurchase: "Non autorisé - Seul le manager peut modifier les factures d'achat",
    notAllowedManagerOnlyDeletePurchase: "Non autorisé - Seul le manager peut supprimer les factures d'achat",
    generalSupplier: "Fournisseur général",

    // Category Management Notifications
    pleaseEnterCategoryName: "Veuillez saisir le nom de la catégorie",
    categoryAlreadyExists: "La catégorie existe déjà",
    categoryAddedSuccessfully: "Catégorie ajoutée",

    // Customer Management Notifications
    pleaseEnterCustomerName: "Veuillez saisir le nom du client",
    customerAddedSuccessfully: "Client ajouté avec succès",
    customerDeletedSuccessfully: "Client supprimé avec succès",
    customerDataUpdatedSuccessfully: "Données client mises à jour avec succès",

    // Product Management Notifications
    pleaseEnterProductName: "Veuillez saisir le nom du produit",

    // Toast Notifications
    clearAllNotifications: "Effacer toutes les notifications",


    scanBarcodeToAddProduct: "Scannez le code-barres - appuyez sur Entrée pour ouvrir la facture",
    openInvoiceWithProduct: "Ouvrir la facture avec le produit",
    openInvoice: "Ouvrir la facture",
    dashboardBarcodeHelp: "Scannez le code-barres pour afficher les informations, appuyez sur Entrée ou le bouton ouvrir la facture pour ajouter le produit à la facture",
    scanProductToDisplay: "Scannez un produit pour afficher les informations",
    productInfo: "Informations du produit",

    // NEW: Barcode Scanning Messages
    productOutOfStock: "Produit en rupture de stock",
    quantityExceedsStock: "La quantité dépasse le stock disponible",
    available: "disponible",
    quantityIncreased: "Quantité augmentée",
    productAdded: "Produit ajouté",
    productNotFound: "Produit non trouvé avec ce code-barres",
    barcodeMinLength: "Code-barres trop court - doit contenir au moins 3 caractères",
    noInvoiceBeingEdited: "Aucune facture en cours de modification",

    // Barcode & Product Search Notifications
    productAddedToInvoiceAutomatically: "Ajouté",
    toInvoiceAutomatically: "à la facture automatiquement",
    productFoundWithBarcode: "Trouvé",
    noProductFoundWithBarcode: "Aucun produit trouvé avec ce code-barres",
    autoBarcodeGenerated: "Code-barres automatique généré",
    barcodeCleared: "Code-barres effacé",
    barcodeAlreadyExists: "Le code-barres existe déjà dans un autre produit",

    // Login & Authentication Notifications
    welcomeUser: "Bienvenue",
    accountInactiveContactManager: "Votre compte est inactif, veuillez contacter le manager",
    invalidUsernameOrPassword: "Nom d'utilisateur ou mot de passe incorrect",

    // Invoice Details Modal
    invoiceDetails: "Détails de la facture",
    salesInvoice: "Facture de vente",
    invoiceInfo: "Informations de la facture",
    customerInfo: "Informations du client",
    customerName: "Nom du client",
    creationTime: "Heure de création",

    // Thermal Printing
    invoiceNumberLabel: "Facture n°:",
    dateLabel: "Date:",
    timeLabel: "Heure:",
    customerLabel: "Client:",
    paymentMethodLabel: "Mode de paiement:",
    productsLabel: "Produits",
    subtotalLabel: "Sous-total:",
    discountLabel: "Remise:",
    taxLabel: "Taxe",
    finalTotalLabel: "Total final:",
    thankYouMessage: "Merci de votre visite",
    printedAtLabel: "Imprimé le:",
    salesInvoiceTitle: "Facture de vente",
    accountingSystem: "Système comptable",
    developedBy: "Développé par",

    // Enhanced Thermal Printing
    thermalInvoice: "Facture thermique",
    thermalPrintError: "Erreur d'impression thermique",
    printError: "Erreur d'impression",
    popupBlocked: "Fenêtre popup bloquée - Veuillez autoriser les popups",
    directPrintingEnabled: "Impression directe activée",
    thermalPrinterDetected: "Imprimante thermique détectée",
    thermalPrinterNotDetected: "Imprimante thermique non détectée",
    printingDirectly: "Impression directe en cours...",
    printingSentToThermal: "Impression envoyée à l'imprimante thermique",
    storeNumber: "Numéro du magasin",
    qtyColumn: "QNT",

    // Invoice Details Modal - Complete
    invoiceNumberColon: "Numéro de facture:",
    dateColon: "Date:",
    creationTimeColon: "Heure de création:",
    customerInfoTitle: "Informations du client",
    customerNameColon: "Nom du client:",
    paymentMethodColon: "Mode de paiement:",
    productsTitle: "Produits",
    productColumn: "Produit",
    quantityColumn: "Quantité",
    priceColumn: "Prix",
    totalColumn: "Total",
    noProductsInInvoice: "Aucun produit dans cette facture",
    subtotalColon: "Sous-total:",
    taxColon: "Taxe",
    discountColon: "Remise:",
    finalTotalColon: "Total final:",
    normalPrintButton: "Impression normale",
    thermalPrintButton: "Impression thermique",
    closeButton: "Fermer",
    backToTop: "Retour en haut",

    // Invoice Details Modal - Complete
    invoiceNumberColon: "Numéro de facture:",
    dateColon: "Date:",
    creationTimeColon: "Heure de création:",
    customerInfoTitle: "Informations du client",
    customerNameColon: "Nom du client:",
    paymentMethodColon: "Mode de paiement:",
    productsTitle: "Produits",
    productColumn: "Produit",
    quantityColumn: "Quantité",
    priceColumn: "Prix",
    totalColumn: "Total",
    noProductsInInvoice: "Aucun produit dans cette facture",
    subtotalColon: "Sous-total:",
    taxColon: "Taxe",
    discountColon: "Remise:",
    finalTotalColon: "Total final:",
    normalPrintButton: "Impression normale",
    thermalPrintButton: "Impression thermique",
    closeButton: "Fermer",
    backToTop: "Retour en haut",

    // Return Process Notifications
    pleaseSelectAtLeastOneProduct: "Veuillez sélectionner une quantité de retour pour au moins un produit",
    productsReturnedSuccessfully: "Produits retournés avec succès",
    andStockUpdated: "et stock mis à jour",

    // Product Modal - Additional Keys
    editExistingProduct: "Modifier le produit existant",
    scanBarcodeOrEnter: "Scannez le code-barres ou saisissez-le manuellement - sera généré automatiquement si laissé vide",
    generateAutoBarcode: "Générer un code-barres automatique",
    clearBarcode: "Effacer le code-barres",
    barcodeHelp: "Vous pouvez scanner le code-barres avec un lecteur ou le saisir manuellement. Si le code-barres existe dans l'inventaire, les informations du produit seront affichées pour modification. Si laissé vide, un code-barres sera généré automatiquement lors de l'enregistrement.",
    currentBarcode: "Code-barres actuel",
    barcodeWillShow: "Le code-barres sera affiché ici",
    currentQuantity: "Quantité actuelle",
    saveProduct: "Enregistrer le produit",
    manageCategories: "Gérer les catégories",
    productNameAlreadyExists: "Le nom du produit existe déjà",
    pleaseChooseDifferentName: "Veuillez choisir un nom différent",
    productExistsWithBarcode: "Un produit existe avec ce code-barres",
    checkBeforeSaving: "Vérifiez avant d'enregistrer",
    barcodeAlreadyExists: "Le code-barres existe déjà",
    usedByProduct: "utilisé par le produit",
    customerNameAlreadyExists: "Le nom du client existe déjà",
    supplierNameAlreadyExists: "Le nom du fournisseur existe déjà",
    sellerNameAlreadyExists: "Le nom du vendeur existe déjà",
    phoneNumberAlreadyExists: "Le numéro de téléphone existe déjà",
    usedByCustomer: "utilisé par le client",
    usedBySupplier: "utilisé par le fournisseur",
    usedBySeller: "utilisé par le vendeur",
    productFoundMessage: "Produit trouvé",
    canEditAndSave: "vous pouvez modifier les informations et enregistrer",
    barcodeScannedSuccess: "Code-barres scanné avec succès - nouveau code-barres",
    barcodeAvailable: "Code-barres disponible pour utilisation - nouveau produit",

    // Category Management Modal
    addNewCategory: "Ajouter une nouvelle catégorie",
    newCategoryName: "Nom de la nouvelle catégorie",
    existingCategories: "Catégories existantes",
    edit: "Modifier",

    // Toast Messages for Products
    productFoundMessage: "Produit trouvé",
    canEditAndSave: "Vous pouvez modifier les informations et enregistrer",
    barcodeScannedSuccess: "Code-barres scanné avec succès - nouveau code-barres",
    barcodeAvailable: "Code-barres disponible pour utilisation - nouveau produit",
    autoBarcodeGenerated: "Code-barres automatique généré",
    barcodeCleared: "Code-barres effacé",

    // Messages
    loginSuccess: "Connexion réussie",
    loginError: "Erreur de nom d'utilisateur ou mot de passe",
    invoiceSaved: "Facture enregistrée avec succès",
    customerAdded: "Client ajouté avec succès",
    customerDeleted: "Client supprimé avec succès",
    productAdded: "Produit ajouté avec succès",
    settingsSaved: "Paramètres enregistrés avec succès",
    pleaseSelectCustomer: "Veuillez sélectionner un client",
    pleaseAddProducts: "Veuillez ajouter des produits à la facture",
    productNotFound: "Aucun produit trouvé avec ce code-barres",

    // Activation Dialog
    activationTitle: "Activation du Programme",
    activationDescription: "Veuillez entrer votre code d'activation pour activer le programme à vie",
    activationCodeLabel: "Code d'activation :",
    activationCodePlaceholder: "ICAL-2025-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX",
    activateProgram: "Activer le Programme",
    activating: "Activation en cours...",
    resetActivation: "Réinitialiser l'Activation (Test)",
    resetActivationTooltip: "Réinitialiser l'activation pour les tests",
    deviceInfo: "Informations de l'Appareil :",
    deviceId: "ID de l'Appareil :",
    deviceNote: "L'activation sera liée de façon permanente à cet appareil",
    getActivationCode: "Pour obtenir le code d'activation :",
    phone: "Téléphone :",
    email: "Email :",
    website: "Site Web :",

    // Activation Error Messages
    pleaseEnterActivationCode: "Veuillez entrer le code d'activation",
    unexpectedActivationError: "Une erreur inattendue s'est produite lors de l'activation",
    programAlreadyActivated: "Le programme est déjà activé sur cet appareil\n\n💡 Pour tester : tapez \"reset\" ou \"test\" pour afficher l'option de réinitialisation",
    confirmResetActivation: "Êtes-vous sûr de vouloir réinitialiser l'activation ?\n\nL'activation actuelle sera supprimée et vous pourrez tester un nouveau code.",
    resetActivationSuccess: "✅ Réinitialisation de l'activation réussie !\n\nVous pouvez maintenant tester un nouveau code d'activation.",
    invalidActivationCodeFormat: "Format du code d'activation incorrect",

    // Reports and Statistics
    reportsAndStatistics: "Rapports et statistiques",
    comprehensiveReports: "Rapports complets pour toutes les opérations financières et commerciales - Manager uniquement",

    // Financial Reports
    financialReports: "Rapports financiers",
    financialReportsDesc: "Rapports des ventes, achats et bénéfices",
    salesReport: "Rapport des ventes",
    salesReportDesc: "Total des ventes et factures",
    purchaseReport: "Rapport des achats",
    purchaseReportDesc: "Total des achats et fournisseurs",
    profitReport: "Rapport des bénéfices",
    profitReportDesc: "Analyse des profits et pertes",

    // Inventory Reports
    inventoryReports: "Rapports de stock",
    inventoryReportsDesc: "État du stock et des produits",
    generalInventoryReport: "Rapport de stock général",
    generalInventoryReportDesc: "Tous les produits et état du stock",
    lowStockReport: "Rapport de stock faible",
    lowStockReportDesc: "Produits nécessitant un réapprovisionnement",
    outOfStockReport: "Rapport de produits épuisés",
    outOfStockReportDesc: "Produits non disponibles",

    // Customer Reports
    customerReports: "Rapports clients",
    customerReportsDesc: "Analyse des clients et des ventes",
    customersReport: "Rapport des clients",
    customersReportDesc: "Liste des clients et leurs données",
    debtorsReport: "Rapport des débiteurs",
    debtorsReportDesc: "Clients débiteurs et créances",
    topCustomersReport: "Meilleurs clients",
    topCustomersReportDesc: "Clients avec le plus d'achats",
    customerAnalysisReport: "Analyse des clients",
    customerAnalysisReportDesc: "Statistiques détaillées des clients",

    // Performance Reports
    performanceReports: "Rapports de performance",
    performanceReportsDesc: "Indicateurs de performance et statistiques",
    dailyReport: "Rapport quotidien",
    dailyReportDesc: "Résumé des opérations quotidiennes",
    weeklyReport: "Rapport hebdomadaire",
    weeklyReportDesc: "Résumé des opérations hebdomadaires",
    monthlyReport: "Rapport mensuel",
    monthlyReportDesc: "Résumé des opérations mensuelles",
    yearlyReport: "Rapport annuel",
    yearlyReportDesc: "Résumé des opérations annuelles",
    kpiReport: "Indicateurs de performance",
    kpiReportDesc: "KPIs et mesures de performance",

    // Report Statistics Labels
    totalDebts: "Total des dettes",
    activeCustomer: "Client actif",
    today: "Aujourd'hui",
    currentWeek: "Semaine actuelle",
    currentMonth: "Mois actuel",
    currentYear: "Année actuelle",
    analysis: "Analyse",

    // Additional Reports
    inventoryValueReport: "Rapport de valeur de stock",
    inventoryValueReportDesc: "Valeur totale du stock",
    categoriesReport: "Rapport des catégories",
    categoriesReportDesc: "Analyse des produits par catégories",
    advancedReports: "Rapports avancés",
    advancedReportsDesc: "Rapports détaillés avec filtres avancés",
    businessManagement: "Gestion d'entreprise intégrée",
    quickSummary: "Résumé rapide",
    totalSales: "Total des ventes",
    totalPurchases: "Total des achats",
    netProfit: "Bénéfice net",
    inventoryValue: "Valeur du stock",
    totalValue: "Valeur totale",
    category: "Catégorie",

    // Advanced Reports Form Labels
    reportType: "Type de rapport :",
    date: "Date :",
    month: "Mois :",
    daily: "Quotidien",
    monthly: "Mensuel",
    print: "Imprimer",

    // Cash Flow Report
    cashFlowReport: "Rapport de flux de trésorerie",
    cashFlowReportDesc: "Mouvement des liquidités entrantes et sortantes",
    cashMovement: "Mouvement des liquidités entrantes et sortantes",
    cash: "En espèces",
    inCash: "En espèces",
    accountingSystem: "Système comptable",
    reportDate: "Date du rapport",
    cashManagement: "Gestion de trésorerie",

    // Common Report Labels
    cashSales: "Ventes en espèces",
    creditSales: "Ventes à crédit",
    cashPurchases: "Achats en espèces",
    creditPurchases: "Achats à crédit",
    netCashFlow: "Flux de trésorerie net",
    cashInflow: "Entrée de liquidités",
    cashOutflow: "Sortie de liquidités",
    cashSurplus: "Excédent de trésorerie",
    cashDeficit: "Déficit de trésorerie",
    receivables: "Créances clients",
    payables: "Dettes fournisseurs",
    invoiceNumber: "Numéro de facture",
    customerName: "Nom du client",
    paymentMethod: "Mode de paiement",
    subtotal: "Sous-total",
    tax: "Taxe",
    discount: "Remise",
    finalAmount: "Montant final",
    status: "Statut",
    paid: "Payée",
    debt: "Crédit",
    walkInCustomer: "Client de passage",
    salesManagement: "Gestion des ventes",
    businessManagement: "Gestion d'entreprise intégrée",
    invoiceCount: "Nombre de factures",
    averageInvoice: "Facture moyenne",
    purchaseManagement: "Gestion des achats",

    // Additional Report Labels
    totalPurchases: "Total des achats",
    supplierName: "Nom du fournisseur",
    grossProfit: "Bénéfice brut",
    netProfit: "Bénéfice net",
    operatingExpenses: "Charges d'exploitation",
    profitMargin: "Marge bénéficiaire",
    costOfGoodsSold: "Coût des marchandises vendues",
    profitAnalysis: "Analyse des profits et pertes",
    inventoryManagement: "Gestion des stocks",
    stockValue: "Valeur du stock",
    lowStock: "Stock faible",
    categoryAnalysis: "Analyse des catégories",
    customerAnalysis: "Analyse des clients",
    topCustomers: "Meilleurs clients",
    debtorsAnalysis: "Analyse des débiteurs",
    performanceAnalysis: "Analyse de performance",
    dailyOperations: "Opérations quotidiennes",
    monthlyOperations: "Opérations mensuelles",
    yearlyOperations: "Opérations annuelles",
    kpiAnalysis: "Analyse des indicateurs de performance",

    // Access Control
    accessRestricted: "Accès restreint",
    reportsManagerOnly: "Les rapports et statistiques sont disponibles pour le manager uniquement",
    loginAsManager: "Veuillez vous connecter avec un compte manager pour accéder à cette page",
    backToDashboard: "Retour au tableau de bord",

    // Login
    login: "Connexion",
    username: "Nom d'utilisateur",
    password: "Mot de passe",
    welcomeBack: "Bon retour",
    enterCredentials: "Entrez vos identifiants pour accéder au système",

    // Additional Report Translations
    salesReport: "Rapport des ventes",
    purchaseReport: "Rapport des achats",
    profitReport: "Rapport des bénéfices",
    profitLossReport: "Rapport des profits et pertes",
    inventoryValueReport: "Rapport de valeur d'inventaire",
    financialAnalysis: "Analyse financière",
    inventoryValuation: "Évaluation d'inventaire",
    supplier: "Fournisseur",
    unknownSupplier: "Fournisseur inconnu",
    totalSales: "Total des ventes",
    totalRevenueFromSales: "Revenus totaux des ventes",
    costOfProductsSold: "Coût des produits vendus",
    salariesRentOtherExpenses: "Salaires, loyer et autres charges",
    netProfitLoss: "Bénéfice/Perte net(te)",
    netLoss: "Perte nette",
    net: "net(te)",
    profitPercentageFromSales: "Pourcentage de bénéfice des ventes",
    credit: "Crédit",
    totalInventoryValue: "Valeur totale d'inventaire",
    productCount: "Nombre de produits",
    totalQuantities: "Quantités totales",
    averageProductValue: "Valeur moyenne du produit",
    productName: "Nom du produit",
    quantity: "Quantité",
    buyPrice: "Prix d'achat",

    // Sales Report French Translations
    reportDate: "Date du rapport",
    generationTime: "Heure de génération",
    salesSummary: "Résumé des ventes",
    cashSales: "Ventes en espèces",
    salesInvoiceDetails: "Détails des factures de vente",
    noSalesInvoicesToDisplay: "Aucune facture de vente à afficher",
    additionalStatistics: "Statistiques supplémentaires",
    totalTaxes: "Total des taxes",
    totalDiscounts: "Total des remises",
    creditInvoices: "Factures à crédit",
    creditValue: "Valeur des crédits",
    accountingSystemIntegratedBusinessManagement: "Système comptable - Gestion d'entreprise intégrée",
    reportGeneratedAutomatically: "Ce rapport a été généré automatiquement par le système comptable conçu par iCode DZ",
    allRightsReserved: "Tous droits réservés",
    salesReportOpened: "Rapport des ventes ouvert pour impression",

    // Customer Report Translations
    customersReport: "Rapport des clients",
    customerManagement: "Gestion des clients",
    activeCustomers: "Clients actifs",
    newCustomersThisMonth: "Nouveaux clients ce mois",
    averagePurchasesPerCustomer: "Achats moyens par client",
    customerNumber: "Numéro client",
    phoneNumber: "Numéro de téléphone",
    registrationDate: "Date d'inscription",
    purchaseCount: "Nombre d'achats",
    notSpecified: "Non spécifié",
    active: "Actif",
    inactive: "Inactif",

    // Additional Report Translations
    debtorsReport: "Rapport des débiteurs",
    bestCustomers: "Meilleurs clients",
    customerAnalysis: "Analyse des clients",
    dailyReport: "Rapport quotidien",
    advancedReports: "Rapports avancés",
    monthlyReport: "Rapport mensuel",
    annualReport: "Rapport annuel",
    performanceIndicators: "Indicateurs de performance",
    categoryReport: "Rapport des catégories",
    generalStockReport: "Rapport de stock général",
    lowStockReport: "Rapport de stock faible",

    // Footer Translations
    reportGeneratedBy: "Ce rapport a été généré automatiquement par le système comptable conçu par iCode DZ",
    allRightsReserved: "© 2025 iDesign DZ +213 551 93 05 89 - Tous droits réservés",

    // New Report System Translations
    newSystem: "Nouveau système",
    accurateCalculations: "Calculs précis à 100%",
    costOfGoodsSold: "Coût des marchandises vendues",
    grossProfit: "Bénéfice brut",
    beforeExpenses: "Avant frais",
    operatingExpenses: "Frais d'exploitation",
    dailyNetProfit: "Bénéfice net quotidien",
    profit: "Bénéfice",
    loss: "Perte",
    fromSales: "Des ventes",
    processedItems: "Éléments traités",
    from: "De",
    item: "Élément",
    expense: "Frais",
    dailyReportOpenedWithWarnings: "📊 Rapport quotidien ouvert avec avertissements - Veuillez vérifier les données",
    dailyReportOpenedSuccessfully: "📊 Rapport quotidien ouvert avec succès - Calculs précis à 100%",
    annualOperationsSummary: "Résumé des opérations annuelles",
    improvedNewSystem: "Nouveau système amélioré",
    noErrors: "Sans erreurs",
    annualNetProfit: "Bénéfice net annuel",
    margin: "Marge",
    monthlyPerformance: "Performance mensuelle",
    sales: "Ventes",
    cost: "Coût",
    annualReportOpenedSuccessfully: "📊 Rapport annuel ouvert avec succès - Calculs précis à 100%",
    monthlyOperationsSummary: "Résumé des opérations mensuelles",
    monthlyNetProfit: "Bénéfice net mensuel",
    dailyPerformanceForMonth: "Performance quotidienne du mois",
    monthlyReportOpenedSuccessfully: "📅 Rapport mensuel ouvert avec succès - Calculs précis à 100%",
    invalidDataReload: "❌ Données invalides, veuillez recharger la page",
    reportValidationFailed: "⚠️ Échec de la validation du calcul du rapport",
    missingProductsWarning: "⚠️ Avertissement : produits manquants",
    dailyReportGenerated: "📊 Rapport quotidien généré",
    monthlyReportGenerated: "📊 Rapport mensuel généré",
    generateReportFirst: "⚠️ Veuillez d'abord générer le rapport",
    reportOpenedForPrint: "🖨️ Rapport ouvert pour impression",

    // Report Toast Messages
    salesReportOpened: "📈 Rapport des ventes ouvert pour impression",
    purchaseReportOpened: "📉 Rapport des achats ouvert pour impression",
    profitReportOpened: "💎 Rapport des bénéfices ouvert pour impression",
    inventoryValueReportOpened: "💰 Rapport de valeur d'inventaire ouvert pour impression",
    cashFlowReportOpened: "💸 Rapport de flux de trésorerie ouvert pour impression",
    categoriesReportOpened: "🏷️ Rapport des catégories ouvert pour impression",
    customersReportOpened: "👤 Rapport des clients ouvert pour impression",
    debtorsReportOpened: "💳 Rapport des débiteurs ouvert pour impression",
    topCustomersReportOpened: "🌟 Rapport des meilleurs clients ouvert pour impression",
    customerAnalysisReportOpened: "📊 Analyse avancée des clients ouverte pour impression",
    kpiReportOpened: "🎯 Rapport des indicateurs de performance ouvert pour impression",

    // Advanced Reports Content Translations
    creationDate: "Date de création",
    soldProductsDetails: "Détails des produits vendus",
    totalCost: "Coût total",

    // KPI Report Translations
    kpiDashboard: "Tableau de bord KPI",
    performanceOverview: "Vue d'ensemble des performances",
    allPeriods: "Toutes les périodes",
    margin: "marge",
    growth: "croissance",
    financialPerformance: "Performance financière",
    customerMetrics: "Métriques clients",
    inventoryMetrics: "Métriques d'inventaire",
    operationalMetrics: "Métriques opérationnelles",
    uniqueCustomers: "Clients uniques",
    averageOrderValue: "Valeur moyenne de commande",
    customersWithDebt: "Clients avec dettes",
    inventoryTurnover: "Rotation des stocks",
    averageDailySales: "Ventes quotidiennes moyennes",
    salesVelocity: "Vélocité des ventes",
    cashRatio: "Ratio de liquidités",
    monthlyGrowthRate: "Taux de croissance mensuel",
    excellent: "Excellent",
    good: "Bon",
    warning: "Attention",

    // Debtors Report Translations
    debtorsAndReceivables: "Débiteurs et créances",
    totalOutstandingAmount: "Montant total en souffrance",
    followUpRequired: "Suivi requis pour le recouvrement de ces montants",
    debtorCustomers: "Clients débiteurs",
    outstandingInvoices: "Factures en souffrance",
    averageDebtPerCustomer: "Dette moyenne par client",
    highestOutstandingAmount: "Montant le plus élevé en souffrance",
    customerName: "Nom du client",
    invoiceCount: "Nombre de factures",
    totalOutstanding: "Total en souffrance",
    oldestInvoice: "Facture la plus ancienne",
    newestInvoice: "Facture la plus récente",
    priorityLevel: "Niveau de priorité",
    high: "Élevé",
    medium: "Moyen",
    low: "Faible",

    // Top Customers Report Translations
    topCustomers: "Meilleurs clients",
    topCustomersAnalysis: "Analyse des clients les plus performants",
    topCustomer: "Client principal",
    totalSalesTopTen: "Total des ventes des 10 meilleurs",
    averagePurchasesTopCustomer: "Achats moyens du client principal",
    totalInvoicesCount: "Nombre total de factures",
    percentageOfTotalSales: "Pourcentage du total des ventes",
    ranking: "Classement",
    totalPurchases: "Total des achats",
    averageInvoice: "Facture moyenne",
    firstPurchase: "Premier achat",
    lastPurchase: "Dernier achat",
    membershipPeriod: "Période d'adhésion",
    days: "jours",

    // Customer Analysis Report Translations
    advancedCustomerAnalysis: "Analyse avancée des clients",
    businessIntelligence: "Intelligence d'affaires",
    generalCustomerStats: "Statistiques générales des clients",
    newCustomersThisMonth: "Nouveaux clients ce mois",
    customersWithPurchases: "Clients avec achats",
    debtorCustomersCount: "Clients débiteurs",
    conversionRate: "Taux de conversion",
    purchaseFrequencyAnalysis: "Analyse de la fréquence d'achat",
    oneTimeBuyers: "Acheteurs occasionnels",
    occasionalBuyers: "Acheteurs occasionnels",
    regularBuyers: "Acheteurs réguliers",
    frequentBuyers: "Acheteurs fréquents",
    insightsAndRecommendations: "Aperçus et recommandations",
    retentionRate: "Taux de rétention",
    customersReturnToBuy: "des clients reviennent pour acheter à nouveau",
    growthOpportunity: "Opportunité de croissance",
    oneTimePurchaseOpportunity: "client a acheté une seule fois - opportunité pour des campagnes de reciblage",
    loyalCustomers: "Clients fidèles",
    regularPurchasers: "client achètent régulièrement - doivent être soignés avec des offres spéciales",
    customerGrowth: "Croissance des clients",
    newCustomersThisMonthInsight: "nouveau client ce mois",
    positiveGrowth: "- croissance positive",
    needsMarketingImprovement: "- nécessite une amélioration du marketing",
    debtManagement: "Gestion des dettes",
    debtFollowUpNeeded: "client a des dettes - nécessite un suivi pour le recouvrement",

    // Category Report Translations
    categoriesReport: "Rapport des catégories",
    categoryAnalysis: "Analyse des catégories",
    productCount: "Nombre de produits",
    totalQuantities: "Quantités totales",
    totalValue: "Valeur totale",
    inventoryPercentage: "Pourcentage du stock",

    // Excel Export Translations
    productCode: "Code Produit",
    productName: "Nom du Produit",
    barcode: "Code-barres",
    buyPrice: "Prix d'Achat",
    sellPrice: "Prix de Vente",
    availableQuantity: "Quantité Disponible",
    minStock: "Stock Minimum",
    currency: "DZD",
    status: "Statut",
    lowStock: "Faible",
    outOfStock: "Épuisé",
    highStock: "Élevé",
    normalStock: "Normal",
    summary: "Résumé",
    totalProducts: "Total des produits",
    lowStockProducts: "Produits en stock faible",
    outOfStockProducts: "Produits épuisés",
    inventoryReport: "Rapport d'inventaire",

    // Additional KPI Translations
    profitFromSales: "Pourcentage de profit des ventes",
    cashVsCredit: "Ventes en espèces vs crédit",
    todaySales: "Ventes d'aujourd'hui",
    monthlySales: "Ventes mensuelles",
    netProfit: "Bénéfice net",
    profitMargin: "Marge bénéficiaire",
    totalCustomers: "Total des clients",
    activeCustomers: "Clients actifs",
    inventoryValue: "Valeur du stock",
    lowStockItems: "Articles en stock faible",
    outOfStockItems: "Articles épuisés",
    monthlyGrowthRate: "Taux de croissance mensuel",

    // Advanced Report Display Translations
    topProducts: "Meilleurs produits vendus",
    quantitySold: "Quantité vendue",
    totalRevenue: "Revenus totaux",
    profit: "Profit",

    // Additional KPI Detail Translations
    uniqueCustomersDesc: "Nombre de clients uniques",
    customersWithDebtDesc: "Nombre de clients avec des dettes",
    totalDebtDesc: "Montants dus par les clients",
    averageOrdersPerCustomer: "Commandes moyennes par client",
    purchaseFrequency: "Fréquence d'achat",
    largeCustomerBase: "Grande base de clients",
    mediumCustomerBase: "Base de clients moyenne",
    smallCustomerBase: "Petite base de clients",
    lowRatio: "Ratio faible",
    mediumRatio: "Ratio moyen",
    highRatio: "Ratio élevé",
    highLoyalty: "Fidélité élevée",
    mediumLoyalty: "Fidélité moyenne",
    lowLoyalty: "Fidélité faible",
    totalGoodsValue: "Valeur totale des marchandises",
    inventoryInvestment: "Investissement en stock",
    needsRestocking: "Produits nécessitant un réapprovisionnement",
    unavailableProducts: "Produits indisponibles",
    stockSalesSpeed: "Vitesse de vente du stock",
    goodStock: "Bon stock",
    needsMonitoring: "Nécessite surveillance",
    urgentRestocking: "Réapprovisionnement urgent",
    none: "Aucun",
    few: "Peu",
    many: "Beaucoup",
    fastTurnover: "Rotation rapide",
    mediumTurnover: "Rotation moyenne",
    slowTurnover: "Rotation lente",
    excellentProfitMargin: "Marge bénéficiaire excellente indiquant une tarification efficace",
    improveProfitMargin: "Peut améliorer la marge bénéficiaire en révisant les coûts et prix",
    customerManagement: "Gestion des clients",
    goodDebtManagement: "Bonne gestion des dettes et du crédit",
    improveCreditPolicies: "Nécessite amélioration des politiques de crédit et suivi des recouvrements",
    inventoryManagement: "Gestion des stocks",
    appropriateStockLevel: "Niveau de stock approprié",
    improveInventoryPlanning: "Nécessite amélioration de la planification des stocks et réapprovisionnement",
    growth: "Croissance",
    thisMonth: "ce mois",
    needsGrowthStrategies: "Nécessite des stratégies pour stimuler la croissance",
    cashFlow: "Flux de trésorerie",
    healthyCashFlow: "Flux de trésorerie sain avec bon ratio de ventes en espèces",
    improveCashFlow: "Nécessite amélioration du flux de trésorerie et réduction des ventes à crédit",

    // Month Names (French)
    month1: "Janvier",
    month2: "Février",
    month3: "Mars",
    month4: "Avril",
    month5: "Mai",
    month6: "Juin",
    month7: "Juillet",
    month8: "Août",
    month9: "Septembre",
    month10: "Octobre",
    month11: "Novembre",
    month12: "Décembre",

    // Control Panel Tooltips
    disableSounds: "Désactiver les sons",
    enableSounds: "Activer les sons",
    keyboardShortcutsAlwaysActive: "Raccourcis clavier toujours actifs",
    disablePrinter: "Désactiver l'imprimante",
    enablePrinter: "Activer l'imprimante",

    // Dashboard Action Messages
    newPurchaseInvoiceClicked: "Clic sur nouvelle facture d'achat",
    purchaseReportClicked: "Navigation vers la page des achats",
    purchaseStatisticsClicked: "Navigation vers la page des rapports et statistiques",
    willOpenNewPurchaseInvoiceWindow: "📄 Ouverture de la fenêtre de création de nouvelle facture d'achat",
    willOpenDetailedPurchaseReport: "📊 Ouverture du rapport détaillé des achats",
    willOpenDetailedPurchaseStatistics: "📈 Ouverture des statistiques détaillées des achats",

    // Print Functions
    invoiceNumberLabel: "Facture n°:",
    dateLabel: "Date:",
    customerLabel: "Client:",
    paymentMethodLabel: "Mode de paiement:",
    productsLabel: "Produits",
    subtotalLabel: "Sous-total:",
    discountLabel: "Remise:",
    taxLabel: "Taxe",
    finalTotalLabel: "Total final:",
    thankYouMessage: "Merci de votre visite",
    printedAtLabel: "Imprimé le:",
    salesInvoiceTitle: "Facture de vente",
    invoiceInfo: "Informations de la facture",
    customerInfo: "Informations du client",
    customerName: "Nom du client",
    creationTime: "Heure de création",
    paymentInfo: "Informations de paiement",
    invoiceStatus: "Statut de la facture",
    paidStatus: "Payée",
    debtStatus: "Crédit",
    thankYouForDealingWithUs: "Merci de faire affaire avec nous",
    allRightsReserved: "Tous droits réservés",
    printWindowOpened: "Fenêtre d'impression ouverte",

    // Edit Invoice Modal
    editInvoiceTitle: "Modifier la facture",
    invoiceItems: "Articles de la facture",
    actions: "Actions",
    saveChanges: "Enregistrer les modifications",
    cancel: "Annuler",
    insufficientStockForProduct: "Stock insuffisant pour le produit",
    invoiceUpdatedAndStockAdjusted: "Facture mise à jour",
    andStockAdjusted: "et stock ajusté",
    notAllowedManagerOnlyEditInvoices: "Non autorisé - Seul le manager peut modifier les factures",

    // Credit Invoice Validation
    cannotSaveCreditForWalkInCustomer: "Impossible d'enregistrer une facture à crédit pour un client de passage",
    pleaseSelectRegisteredCustomerForCredit: "Veuillez sélectionner un client enregistré pour le paiement à crédit",

    // Sales Report Print
    additionalStatistics: "Statistiques supplémentaires",
    totalTaxes: "Total des taxes",
    totalDiscounts: "Total des remises",
    creditInvoicesCount: "Factures à crédit",
    creditInvoicesValue: "Valeur des crédits",
    professionalSalesReportOpened: "Rapport de ventes professionnel ouvert pour impression",

    // Additional Sales Report Keys
    salesSummary: "Résumé des ventes",
    salesInvoiceDetails: "Détails des factures de vente",
    reportDate: "Date du rapport",
    generationTime: "Heure de génération",
    noSalesInvoicesToDisplay: "Aucune facture de vente à afficher",
    finalTotal: "Total final",
    lastInvoice: "Dernière facture"
  },

    // Dashboard LCD Display
    scanBarcodeToOpenInvoice: "Scannez le code-barres et appuyez sur Entrée pour ouvrir la facture",
    scannerActive: "Scanner Actif",

  en: {
    // Language Selection
    selectLanguage: "Select Language",
    arabic: "Arabic",
    french: "French",
    english: "English",
    languageSelected: "Language selected successfully",

    // Login & Authentication
    welcome: "Welcome",
    loginPrompt: "Sign in to access your account",
    username: "Username",
    password: "Password",
    rememberMe: "Remember me",
    login: "Sign In",
    logout: "Logout",
    systemName: "Accounting System",
    allRightsReserved: "All rights reserved",
    version: "Version",
    systemDescription: "Integrated accounting system to manage your business",
    feature1: "Sales and purchase management with high efficiency",
    feature2: "Inventory management and item monitoring",
    feature3: "Integrated financial and accounting reports",
    feature4: "Easy customer and supplier management",

    // User Management
    systemUser: "System User",
    systemManager: "System Manager",
    seller: "Seller",
    manager: "The Manager",

    // Navigation
    dashboard: "Dashboard",
    products: "Products",
    sales: "Sales",
    customers: "Customers",
    purchases: "Purchases",
    reports: "Reports",
    settings: "Settings",
    users: "Users",

    // Dashboard
    systemOverview: "F6 📦 Add new product F7 📦 New purchase invoice Enter 💾 Save invoice F3 ➕ Add product ESC ❌ Close without saving",
    totalSales: "Total Sales",
    totalInvoices: "Number of Invoices",
    totalProducts: "Total Products",
    lowStockProducts: "Low Stock Products",
    recentInvoicesOperations: "Recent Invoices and Operations",
    quickOperations: "Quick Operations",
    chooseOperation: "Choose the operation you want to perform",

    // Dashboard Stats
    invoice: "invoice",
    invoices: "invoices",
    product: "product",
    products: "products",

    // Table Headers
    invoiceNumber: "Invoice Number",
    date: "Date",
    supplier: "Supplier",
    customer: "Customer",
    amountPaid: "Amount Paid",
    totalAmount: "Total Amount",
    status: "Status",

    // Recent Operations
    recentInvoicesAndOperations: "Recent Invoices and Operations",

    // Status
    paid: "Paid",
    unpaid: "Unpaid",
    debt: "Debt",

    // Customer Names
    walkInCustomer: "Walk-in Customer",

    // Quick Actions
    quickSalesInvoice: "Quick sales invoice",
    quickPurchaseInvoice: "Quick purchase invoice",
    quickAddProduct: "Quick add product",
    newSalesInvoice: "New Sales Invoice",
    newPurchaseInvoice: "New Purchase Invoice",
    purchaseReport: "Purchase Report",
    purchaseStatistics: "Purchase Statistics",
    salesManagement: "Sales management",
    inventoryManagement: "Inventory management",
    customerManagement: "Customer management",

    // Dashboard KPIs and Performance
    performanceOverview: "Performance Overview",
    netProfit: "Net Profit",
    profitMargin: "Profit Margin",
    todaySales: "Today's Sales",
    monthlySales: "Monthly Sales",
    monthlyGrowth: "Monthly Growth",
    allPeriods: "All Periods",
    growth: "Growth",
    margin: "Margin",

    // Units and Currency
    dinar: "dinar",
    dinars: "dinars",
    unit: "unit",
    units: "units",

    totalCustomers: "Total Customers",
    lowStock: "Low Stock",
    recentSales: "Recent Sales",
    topProducts: "Top Products",

    // Sales
    newSalesInvoice: "New Sales Invoice",
    newInvoice: "New Invoice",
    report: "Report",
    totalSales: "Total Sales",
    invoiceCount: "Number of Invoices",
    averageInvoice: "Average Invoice",
    creditInvoices: "Credit Invoices",
    invoiceNumber: "Invoice Number",
    date: "Date",
    customer: "Customer",
    paymentMethod: "Payment Method",
    amount: "Amount",
    status: "Status",
    actions: "Actions",
    noInvoicesSaved: "No invoices saved",
    walkInCustomer: "Walk-in Customer",
    cash: "Cash",
    credit: "Credit",
    paid: "Paid",
    debt: "Debt",
    viewInvoiceDetails: "View invoice details",
    normalPrint: "Normal print",
    thermalPrint: "Thermal print",
    editInvoice: "Edit invoice (Manager only)",
    returnProducts: "Return products",
    deleteInvoice: "Delete invoice (Manager only)",
    selectCustomer: "Select Customer",
    scanBarcode: "Scan Barcode",
    product: "Product",
    quantity: "Quantity",
    price: "Price",
    total: "Total",
    add: "Add",
    subtotal: "Subtotal",
    tax: "Tax",
    discount: "Discount",
    finalTotal: "Final Total",
    saveInvoice: "Save Invoice",
    cancel: "Cancel",
    delete: "Delete",
    view: "View",
    print: "Print",

    // Sales Report
    salesReport: "Sales Report",
    reportDate: "Report Date",
    generationTime: "Generation Time",
    salesSummary: "Sales Summary",
    cashSales: "Cash Sales",
    salesInvoiceDetails: "Sales Invoice Details",
    noSalesInvoicesToDisplay: "No sales invoices to display",
    additionalStatistics: "Additional Statistics",
    totalTaxes: "Total Taxes",
    totalDiscounts: "Total Discounts",
    creditValue: "Credit Value",
    accountingSystemIntegratedBusinessManagement: "Accounting System - Integrated Business Management",
    reportGeneratedAutomatically: "This report was generated automatically by the accounting system designed by iCode DZ",
    allRightsReserved: "All rights reserved",
    salesReportOpened: "Professional sales report opened for printing",

    // Sales Invoice Modal
    saveInvoiceShortcut: "Save Invoice",
    addProductShortcut: "Add Product",
    closeWithoutSaving: "Close without saving",
    scanBarcodeLabel: "Scan Barcode",
    scanBarcodePlaceholder: "Scan barcode or enter manually",
    searchProductPlaceholder: "Search for product (name, code, barcode)...",
    selectProduct: "Select Product",
    available: "Available",
    invoiceItems: "Invoice Items",
    noProductsAdded: "No products added yet",
    useBarcodeOrSelect: "Use barcode or select from list",
    productName: "Product",
    quantityRequiredExceedsStock: "Requested quantity exceeds available stock",
    deleteTitle: "Delete",
    selectRegisteredCustomer: "Select registered customer",
    action: "Action",
    activeBarcodeReader: "Active barcode reader",

    // Sales Invoice Barcode Scanner
    salesBarcodeScanner: "Sales Barcode Scanner",
    scanToAddProduct: "Scan barcode to add product",
    barcodeActive: "Barcode scanner active",
    clearBarcode: "Clear barcode",
    productDisplay: "Product Display",
    waitingForScan: "Waiting for barcode scan...",
    activeBarcodeScanner: "Active - Scan Barcode",
    clear: "Clear",

    // Purchase Management
    purchaseManagement: "Purchase Management",
    newPurchaseInvoice: "New Purchase Invoice",
    totalPurchases: "Total Purchases",
    purchaseInvoiceCount: "Number of Purchase Invoices",
    averagePurchaseInvoice: "Average Purchase Invoice",
    creditPurchaseInvoices: "Credit Invoices",
    supplier: "Supplier",
    noPurchaseInvoicesSaved: "No purchase invoices saved",
    generalSupplier: "General Supplier",
    editPurchaseInvoice: "Edit purchase invoice (Manager only)",
    deletePurchaseInvoice: "Delete purchase invoice (Manager only)",

    // Purchase Invoice Modal
    editPurchaseInvoiceTitle: "Edit Purchase Invoice",
    savePurchaseInvoice: "Save Purchase Invoice",
    updatePurchaseInvoice: "Update Purchase Invoice",
    purchaseInvoiceItems: "Purchase Invoice Items",
    noPurchaseProductsAdded: "No products added yet",
    selectProductsFromList: "Select products from the list above",
    selectRegisteredSupplier: "Select registered supplier",
    addNewSupplier: "+ Add new supplier",
    purchasePrice: "Purchase Price",
    addProductShortcut: "Add Product",
    closeWithoutSaving: "Close without saving",
    selectProduct: "Select product",
    available: "Available",
    addNewProduct: "Add new product",
    quantity: "Quantity",
    add: "Add",
    productName: "Product",
    total: "Total",
    action: "Action",
    delete: "Delete",
    discount: "Discount",
    subtotal: "Subtotal",
    tax: "Tax",
    finalTotal: "Final Total",
    cancel: "Cancel",

    // Customers
    customersManagement: "Customers Management",
    addNewCustomer: "Add New Customer",
    customerName: "Customer Name",
    phone: "Phone",
    email: "Email",
    address: "Address",
    company: "Company",
    balance: "Balance",
    creditLimit: "Credit Limit",
    paymentTerm: "Payment Term",
    status: "Status",
    active: "Active",
    inactive: "Inactive",
    actions: "Actions",

    // Customer Management Page
    totalCustomers: "Total Customers",
    totalDues: "Total Dues",
    debtorCustomers: "Debtor Customers",
    customerNumber: "Customer Number",
    profitMarginDiscount: "Discount (%)",
    editCustomer: "Edit Customer",
    deleteCustomer: "Delete Customer",
    searchCustomers: "Search customers (name, phone, email)...",
    searchSalesInvoices: "Search sales invoices (invoice number, customer name)...",
    searchPurchaseInvoices: "Search purchase invoices (invoice number, supplier name)...",
    noCustomersMatchingFilter: "No customers matching filter",
    noCustomersAdded: "No customers added",
    noInvoicesMatchingFilter: "No invoices matching filter",
    noPurchaseInvoicesMatchingFilter: "No purchase invoices matching filter",
    addNewCustomerTitle: "Add New Customer",
    customerID: "Customer ID",
    companyName: "Company Name",
    phoneNumber: "Phone Number",
    emailAddress: "Email Address",
    fullAddress: "Full Address",
    openingBalance: "Opening Balance",
    creditLimitAmount: "Credit Limit",
    paymentTermDays: "Payment Term (in days)",
    save: "Save",
    cancel: "Cancel",
    customerSavedSuccess: "Customer data saved successfully",
    discountAppliedToProfit: "Discount is applied to profit margin, not total sales",

    // Customer Actions
    printCustomerData: "Print Customer Data",
    thermalPrintCustomer: "Thermal Print Customer",
    viewCustomerOperations: "View Customer Operations",
    confirmDeleteCustomer: "Are you sure you want to delete this customer?",
    yes: "Yes",
    no: "No",
    customerDeletedSuccessfully: "Customer deleted successfully",
    customerOperationsTitle: "Customer Operations",
    noOperationsFound: "No operations found for this customer",
    customerReport: "Customer Report",

    // Payment functionality
    payCustomerDebt: "Pay Customer Bills",
    paymentDetails: "Payment Details",
    paymentAmount: "Payment Amount",
    maxPaymentAmount: "Maximum Amount",
    currentDebt: "Current Debt",
    remainingBalance: "Remaining Balance",
    processPayment: "Process Payment",
    paymentProcessedSuccessfully: "Payment processed successfully",
    pleaseEnterValidAmount: "Please enter a valid amount",
    paymentExceedsBalance: "Payment amount exceeds customer balance",
    for: "for",

    // Customer Operations
    customerOperations: "Customer Operations",
    operationType: "Operation Type",
    sale: "Sale",
    payment: "Payment",
    paymentReceived: "Payment Received",
    totalSales: "Total Sales",
    totalPayments: "Total Payments",
    currentBalance: "Current Balance",

    // Customer Operations with Payment
    customerOperationsWithPayment: "Customer Operations & Payments",
    paymentHistory: "Payment History",
    makePayment: "Make Payment",
    paymentDate: "Payment Date",
    paymentReference: "Payment Reference",
    noPaymentsFound: "No payments found for this customer",
    totalPaid: "Total Paid",
    outstandingBalance: "Outstanding Balance",
    lastPayment: "Last Payment",
    paymentSuccessful: "Payment successful",
    enterPaymentAmount: "Enter payment amount",
    paymentNotes: "Payment Notes",

    // Print transactions
    customerTransactionsReport: "Customer Transactions Report",
    transactionsAnalysis: "Transactions Analysis",
    transactionsSummary: "Transactions Summary",
    totalTransactions: "Total Transactions",
    cashTransactions: "Cash Transactions",
    creditTransactions: "Credit Transactions",
    transactionsList: "Transactions List",
    transactionsReportOpened: "Transactions report opened for printing",
    thermalTransactionsReportOpened: "Transactions report opened for thermal printing",
    printA4: "Print A4",
    printThermal: "Thermal Print",
    creditOperations: "Credit Operations",
    addNewCustomer: "Add New Customer",

    // Products & Inventory Management
    productsManagement: "Products Management",
    inventoryManagement: "Inventory Management",
    addNewProduct: "Add New Product",
    newProduct: "New Product",
    productName: "Product Name",
    category: "Category",
    barcode: "Barcode",
    stock: "Stock",
    minStock: "Min Stock",

    // Inventory Page Header
    dataManagement: "Data",
    exportExcel: "Export Excel",
    jsonBackup: "JSON Backup",
    importData: "Import Data",
    deleteAllData: "Delete All Data",
    print: "Print",

    // Search and Filters
    searchProducts: "Search products (name, code, barcode)...",
    allCategories: "All Categories",
    allStatuses: "All Statuses",
    manageCategories: "Manage Categories",
    clearFilters: "Clear Filters",

    // Table Headers
    productCode: "Product Code",
    productName: "Product Name",
    barcode: "Barcode",
    category: "Category",
    buyPrice: "Buy Price",
    sellPrice: "Sell Price",
    availableQuantity: "Available Quantity",
    minStock: "Min Stock",
    totalValue: "Total Value",
    status: "Status",
    actions: "Actions",

    // Product Status
    normal: "Normal",
    high: "High",
    low: "Low",
    outOfStock: "Out of Stock",
    notSpecified: "Not Specified",

    // Table Messages
    noProductsFound: "No products match search criteria",
    noProductsInInventory: "No products in inventory",
    clickToEdit: "Click to edit",
    currentBarcode: "Current Barcode",
    shiftEnterSaveAndPrint: "Shift+Enter: Invoice saved and sent to thermal printer",
    enterSaveOnly: "Enter: Invoice saved",
    addProductByBarcode: "Add Product by Barcode",
    scanBarcodeToAddProduct: "Scan barcode to add product to invoice",
    addProductFromList: "Add Product from List",
    selectProductOption: "Select a product",

    // Summary Cards
    displayedProducts: "Displayed Products",
    totalProducts: "Total Products",
    totalValue: "Total Value",
    lowStockProducts: "Low Stock Products",
    outOfStockProducts: "Out of Stock Products",
    outOfTotal: "out of",

    // Product Actions
    editProduct: "Edit (Manager only)",
    deleteProduct: "Delete (Manager only)",
    requestStockUpdate: "Request quantity update (requires manager approval)",
    managerOnly: "Manager only",

    // Bulk Selection
    selectAll: "Select All",
    selected: "selected",
    deleteSelected: "Delete Selected",
    pleaseSelectItems: "Please select items to delete",
    confirmDeleteSelected: "Are you sure you want to delete the selected items?",
    selectedItemsDeleted: "Selected items deleted",
    andStockRestored: "and products returned to stock",
    andStockAdjusted: "and stock adjusted",
    stockWillBeAdjusted: "Stock will be adjusted automatically",
    allProductsWillBeRestored: "All products will be returned to stock",
    notAllowedManagerOnlyBulkDelete: "Not allowed - Only manager can perform bulk delete",

    // Product Modal
    editExistingProduct: "Edit Existing Product",
    addNewProduct: "Add New Product",
    scanBarcode: "Scan Barcode",
    scanBarcodeOrEnter: "Scan barcode or enter manually - auto-generation if left empty",
    generateAutoBarcode: "Generate Auto Barcode",
    clearBarcode: "Clear Barcode",
    barcodeHelp: "You can scan the barcode using a barcode reader or enter it manually. If the barcode exists in inventory, product information will be displayed for editing. If left empty, a barcode will be generated automatically upon saving.",
    productNumber: "Product Number",
    enterProductName: "Enter product name",
    selectCategory: "Select category",
    currentBarcode: "📷 Current Barcode",
    barcodeWillShow: "Barcode will be displayed here",
    buyPrice: "Buy Price",
    sellPrice: "Sell Price",
    currentQuantity: "Current Quantity",
    minStock: "Min Stock",
    saveChanges: "Save Changes",
    saveProduct: "Save Product",
    cancel: "Cancel",

    // Category Management Modal
    categoryManagement: "Product Categories Management",
    addNewCategory: "Add New Category",
    newCategoryName: "New category name",
    add: "Add",
    existingCategories: "Existing Categories",
    edit: "Edit",
    delete: "Delete",

    // Settings
    settings: "Settings",
    storeAndSellersManagement: "Store and sellers settings management",
    storeSettings: "Store Settings",
    editSettings: "Edit Settings",
    storeName: "Store Name",
    phoneNumber: "Phone Number",
    storePhone: "Store Phone",
    storeAddress: "Store Address",
    address: "Address",
    currency: "Currency",
    taxRate: "Tax Rate",
    language: "Language",
    theme: "Theme",
    lightMode: "Light Mode",
    darkMode: "Dark Mode",
    save: "Save",
    saveSettings: "Save Settings",

    // Seller Management
    sellersManagement: "Sellers Management",
    addNewSeller: "Add New Seller",
    sellerNumber: "Number",
    sellerName: "Name",
    username: "Username",
    phone: "Phone",
    role: "Role",
    status: "Status",
    creationDate: "Creation Date",
    actions: "Actions",
    admin: "Administrator",
    seller: "Seller",
    active: "Active",
    inactive: "Inactive",
    activate: "Activate",
    deactivate: "Deactivate",
    delete: "Delete",

    // Seller Modal
    addNewSellerTitle: "Add New Seller",
    sellerID: "Seller ID",
    sellerNameLabel: "Seller Name",
    usernameLabel: "Username",
    passwordLabel: "Password",
    phoneLabel: "Phone Number",
    emailLabel: "Email Address",
    roleLabel: "Role",
    enterSellerName: "Enter seller name",
    enterUsername: "Enter username",
    enterPassword: "Enter password",
    enterPhone: "Enter phone number",
    enterEmail: "Enter email address",
    selectRole: "Select role",

    // Store Settings Modal
    storeSettingsModal: "Store Settings",
    storeNameRequired: "Store Name",
    enterStoreName: "Enter store name",
    storeNumberLabel: "Store Number",
    storeNumberPlaceholder: "ST001",
    phoneNumberLabel: "Phone Number",
    phoneNumberPlaceholder: "+*********** 456",
    addressLabel: "Address",
    fullStoreAddress: "Full store address",
    taxRateLabel: "Tax Rate (%)",
    taxRatePlaceholder: "19",
    currencyLabel: "Currency",
    algerianDinar: "Algerian Dinar (DZD)",
    usDollar: "US Dollar (USD)",
    euro: "Euro (EUR)",
    adminPasscodeLabel: "Admin Passcode",
    adminPasscodePlaceholder: "010290",
    adminPasscodeHelp: "Passcode required to delete and edit sensitive data",
    storeLogo: "Store Logo",
    logoPreview: "Store Logo",

    // Edit Customer Modal
    editCustomerData: "Edit Customer Data",
    customerID: "Customer ID",
    customerNameRequired: "Customer Name",
    enterCustomerName: "Enter customer name",
    phoneNumberPlaceholder: "Phone number",
    emailPlaceholder: "Email address",
    addressPlaceholder: "Address",
    companyPlaceholder: "Company name",
    openingBalance: "Opening Balance",
    creditLimit: "Credit Limit",
    paymentTermDays: "Payment Term (in days)",
    profitMarginDiscount: "Profit Margin Discount (%)",
    discountPercentagePlaceholder: "Discount percentage on profit margin",
    discountAppliedToProfit: "Discount is applied to profit margin, not total sales",
    saveChanges: "Save Changes",

    // Expenses Modal
    editExpense: "Edit Expense",
    addNewExpense: "Add New Expense",
    date: "Date",
    category: "Category",
    amount: "Amount",
    paymentMethod: "Payment Method",
    description: "Description",
    expenseDescription: "Enter detailed expense description",
    salariesWages: "Salaries and Wages",
    rent: "Rent",
    utilities: "Utilities (electricity, water, internet)",
    taxesFees: "Taxes and Fees",
    marketingAdvertising: "Marketing and Advertising",
    maintenanceRepairs: "Maintenance and Repairs",
    transportationTravel: "Transportation and Travel",
    otherExpenses: "Other Expenses",

    // Repair Management Translations
    repairManagement: "Repair Management",
    repairs: "Repairs",
    newBonPour: "New Bon Pour",
    repairCompleted: "Repair Completed",
    clientPickup: "Client Pickup",
    repairOrder: "Repair Order",
    createRepairOrder: "Create Repair Order",

    // Form Fields
    clientDeviceInfo: "Client Device Info",
    clientFullName: "Client Full Name",
    problemTypeInfo: "Problem Type & Description",
    pricingPaymentInfo: "Pricing & Payment",
    dateDetailsInfo: "Date & Details",
    deviceName: "Device Name",
    deviceType: "Device Type",
    problemDescription: "Problem Description",
    repairPrice: "Repair Price",
    partialPayment: "Partial Payment",
    paymentStatus: "Payment Status",
    depositDate: "Deposit Date",
    depositTime: "Deposit Time",
    repairBarcode: "Repair Barcode",
    remarks: "Remarks",

    // Device Types
    smartphone: "Smartphone",
    tablet: "Tablet",
    pc: "PC",
    console: "Console",

    // Problem Types
    lcd: "LCD",
    lcdWithFrame: "LCD With Frame",
    chargingPort: "Charging Port",
    glass: "Glass",
    backGlass: "Back Glass",
    battery: "Battery",
    frontCamera: "Front Camera",
    rearCamera: "Rear Camera",
    microphone: "Microphone",
    loudspeaker: "Loudspeaker",
    earpieceSpeaker: "Earpiece Speaker",
    waterDamage: "Water Damage",
    powerButton: "Power Button",
    volumeButtons: "Volume Buttons",
    homeButton: "Home Button",
    slowPerformance: "Slow Performance",
    androidCorruption: "Android Corruption",
    osCorruption: "OS Corruption",
    wifiIssues: "Wi-Fi Issues",
    bluetoothIssues: "Bluetooth Issues",
    cellularNetworkIssues: "Cellular Network Issues",
    noSound: "No Sound",
    headphoneJackIssues: "Headphone Jack Issues",
    vibratorMotorFailure: "Vibrator Motor Failure",
    proximitySensor: "Proximity Sensor",
    gyroscope: "Gyroscope",
    fingerprintSensor: "Fingerprint Sensor",
    overheating: "Overheating",
    storageFull: "Storage Full",
    backlightIssues: "Backlight Issues",
    housingDamage: "Housing Damage",
    addNewProblem: "Add New Problem",

    // Payment Status
    paid: "Paid",
    partiallyPaid: "Partially Paid",
    unpaid: "Unpaid",

    // Repair Status
    inProcess: "In Process",
    waitingForClient: "Waiting for Client",
    notSuccess: "Not Success",
    done: "Done",

    // Actions
    createAndPrint: "Create and Print",
    cancel: "Cancel",
    viewInfo: "View Info",
    edit: "Edit",
    printQRCode: "Print QR Code",
    printBonPour: "Print Bon Pour",

    // Process Messages
    repairCompletedSuccess: "Repair completed successfully",
    repairCompletedFailure: "Repair completion failed",
    partsChangedPrice: "Parts Changed Price",
    clientPickupCompleted: "Client pickup completed",

    // Table Headers
    clientName: "Client Name",
    deviceNameHeader: "Device Name",
    problem: "Problem",
    repairPriceHeader: "Repair Price",
    partsPrice: "Parts Price",
    interestRate: "Interest Rate",
    situation: "Situation",
    actions: "Actions",

    // Admin Protection
    adminPasscodeRequired: "Admin Passcode Required",
    enterAdminPasscode: "Enter Admin Passcode",
    enterAdminPasscodeToEdit: "Enter admin passcode to edit repair",
    incorrectPasscode: "Incorrect Passcode",
    editRepair: "Edit Repair",
    deleteRepair: "Delete Repair",
    enterPasscode: "Enter Passcode",

    // QR Scanner
    scanQRCode: "Scan QR Code",
    qrScannerActive: "QR Scanner Active",
    qrCodeScanned: "QR Code Scanned",
    qrCode: "QR Code",
    repairQRCode: "Repair QR Code",
    repairDetails: "Repair Details",
    clickToEnlarge: "Click to Enlarge",
    qrGenerationError: "QR Generation Error",
    printTicket: "Print Ticket",
    totalPrice: "Total Price",
    repairCompletionQuestion: "Did the repair succeed?",
    didRepairSucceed: "Did the repair succeed?",
    repairSuccess: "Repair Success",
    repairNotSuccess: "Repair Not Success",
    supplierName: "Supplier Name",
    selectSupplier: "Select Supplier",
    printingQRCode: "Printing QR Code",
    qrCodePrinted: "QR Code Printed",
    printingError: "Printing Error",
    scanQRForRecovery: "Scan QR Code for automatic client information",
    scanForAutoFill: "Scan to auto-fill client and phone information",
    manualSearchPlaceholder: "Or search manually by client name...",
    findRepairToRecover: "Find Repair to Recover",
    scanOrSelectRepair: "Scan QR code or select manually",
    qrCodeScanner: "QR Code Scanner",
    quickestMethod: "Quickest method",
    manualSearch: "Manual Search",
    searchByName: "Search by name",
    selectFromList: "Select from List",
    browseAllReady: "Browse all ready repairs",
    scanCodeBarre: "Scan Code Barre",
    clientInformation: "Client Information",
    phoneNumber: "Phone Number",
    priceBreakdown: "Price Breakdown",
    saveAndComplete: "Save and Complete",
    optionalPrintActions: "Optional: Press Shift + Enter to:",
    completeRecovery: "Complete Recovery",
    printFinalInvoice: "Print Final Invoice",

    // Printing Messages
    printingRepairTicket: "Printing Repair Ticket",
    printingQRCode: "Printing QR Code",
    repairTicketPrinted: "Repair Ticket Printed",
    qrCodePrinted: "QR Code Printed",

    // Enhanced Thermal Printing Messages
    thermalPrintButton: "Thermal Print",
    printThermal: "Thermal Print",
    repairOrdersList: "Repair Orders List",
    repairOrdersListPrinted: "Repair Orders List Printed",
    supplierTransactions: "Supplier Transactions",
    supplierInformation: "Supplier Information",
    transactionsList: "Transactions List",
    transactionCount: "Transaction Count",
    andMore: "and more",
    clientPickupReceipt: "Client Pickup Receipt",
    repairSummary: "Repair Summary",
    repairStatus: "Repair Status",
    successful: "Successful",
    notSuccessful: "Not Successful",
    paymentDetails: "Payment Details",
    finalAmount: "Final Amount",
    pickupTime: "Pickup Time",
    thankYouForTrust: "Thank you for your trust",
    summary: "Summary",
    totalOrders: "Total Orders",
    printDate: "Print Date",
    totals: "Totals",
    totalValue: "Total Value",
    reportGenerated: "Report Generated",
    repairReceipt: "Repair Receipt",
    repairInformation: "Repair Information",
    repairId: "Repair ID",
    pricingDetails: "Pricing Details",

    // New Thermal Printing Translations
    repairTicket: "Repair Ticket",
    repairSystem: "Repair System - ICALDZ",
    ticketNumber: "Ticket Number",
    printedOn: "Printed on",
    clientInvoice: "Client Invoice",
    invoiceNumber: "Invoice Number",
    time: "Time",
    paymentMode: "Payment Mode",
    cash: "Cash",
    service: "Service",
    qty: "Qty",
    price: "Price",
    total: "Total",
    repairParts: "Repair Parts",
    subtotal: "Subtotal",
    tax: "Tax",
    finalTotal: "Final Total",
    thankYouVisit: "Thank you for your visit",

    // Paste Ticket Translations
    printingPasteTicket: "Printing Paste Ticket",
    pasteTicketPrinted: "Paste Ticket Printed",
    printPasteTicket: "Print Paste Ticket",
    noDescription: "No description",
    interestRate: "Interest Rate",

    // Supplier Transaction Translations
    supplierTransactions: "Supplier Transactions",
    supplierName: "Supplier Name",
    totalTransactions: "Total Transactions",
    description: "Description",
    partProblem: "Part Problem",
    partsPrice: "Parts Price",
    date: "Date",
    amount: "Amount",
    status: "Status",
    paid: "Paid",
    pending: "Pending",
    paidAmount: "Paid Amount",
    remainingCredit: "Remaining Credit",
    thankYouBusiness: "Thank you for your business",

    // Additional Repair Translations
    manageRepairOrders: "Manage repair orders and track device status",
    createNewRepairOrder: "Create new repair order",
    markRepairAsCompleted: "Mark repair as completed",
    processClientPickup: "Process client pickup",
    repairOrders: "Repair Orders",
    searchRepairs: "Search repairs...",
    allStatuses: "All Statuses",
    noRepairsFound: "No repairs found",
    pleaseEnterClientName: "Please enter client name",
    pleaseEnterDeviceName: "Please enter device name",
    pleaseSelectDeviceType: "Please select device type",
    pleaseSelectProblemType: "Please select problem type",
    pleaseEnterValidRepairPrice: "Please enter valid repair price",
    repairOrderCreated: "Repair order created successfully",
    canOnlyEditCompletedRepairs: "Can only edit completed repairs",
    repairMarkedAsCompleted: "Repair marked as completed",
    repairMarkedAsFailed: "Repair marked as failed",
    remarksRequiredForFailure: "Remarks required for failure",
    repairNotReadyForPickup: "Repair not ready for pickup",
    repairNotFound: "Repair not found",
    invalidQRCode: "Invalid QR code",
    repairFound: "Repair found",
    enterClientName: "Enter client name",
    enterDeviceName: "Enter device name",
    selectDeviceType: "Select device type",
    selectProblem: "Select problem",
    enterNewProblem: "Enter new problem",
    enterRemarks: "Enter additional remarks",
    autoGenerated: "Auto-generated",
    optional: "Optional",
    explainFailureReason: "Explain failure reason",
    markAsWaitingForClient: "Mark as waiting for client",
    markAsNotSuccess: "Mark as not success",
    scanClientQRCode: "Scan client QR code to display repair information",
    openQRScanner: "Open QR Scanner",
    repairSummary: "Repair Summary",
    totalAmount: "Total Amount",
    completePickup: "Complete Pickup",
    printFinalInvoice: "Print Final Invoice",
    pointCameraAtQR: "Point camera at QR code",
    orEnterManually: "Or enter manually",
    enterRepairCode: "Enter repair code",
    search: "Search",
    repairCompletionInfo: "Choose an in-progress repair to mark it as completed",
    noInProgressRepairs: "No repairs in progress currently",
    createNewRepairFirst: "Create a new repair first or check repair statuses",
    chooseRepairOutcome: "Choose repair outcome to update status accordingly",
    selectSuccessOrFailure: "Select whether the repair succeeded or failed",
    selectRepairToComplete: "Select Repair to Complete",
    chooseInProgressRepair: "Scan QR code or select manually",
    repairSuccessDescription: "Repair completed successfully - will be marked as 'Waiting for Client'",
    repairNotSuccessDescription: "Repair not completed - will be marked as 'Not Success'",
    verificationPriceNote: "Price charged for diagnosis/verification work",
    failureReason: "Failure reason",
    reviewAndComplete: "Review and Complete",
    confirmDetailsAndComplete: "Verify details and finalize the recovery",
    finalPrice: "Final Price (if different)",
    finalPriceNote: "Leave empty to use calculated price",
    selectRepairFromList: "Choose a repair from list...",
    orSearchManually: "Or Search Manually",
    searchByNamePhoneDevice: "Search by name, phone, or device...",
    searchByNamePhoneDate: "Search by name, phone, or date...",
    pricingBreakdown: "Pricing Breakdown",
    noRepairsReady: "No repairs ready for pickup",
    checkRepairStatuses: "Check repair statuses or complete repairs first",
    back: "Back",
    terminerRecuperation: "Complete Recovery",
    printingFinalInvoice: "Printing final invoice",
    finalInvoice: "Final Invoice",
    completionDate: "Completion Date",
    repairCompleted: "Repair completed successfully",
    finalInvoicePrinted: "Final invoice printed",

    // Info Modal Translations
    repairInformation: "Repair Information",
    printA4: "Print A4",
    clientInformation: "Client Information",
    deviceInformation: "Device Information",
    pricingInformation: "Pricing Information",
    statusInformation: "Status Information",
    currentStatus: "Current Status",
    completionRemarks: "Completion Remarks",
    failureRemarks: "Failure Remarks",
    timestamps: "Timestamps",
    createdAt: "Created At",
    completedAt: "Completed At",
    pickedUpAt: "Picked Up At",
    printingError: "Printing Error",
    thankYou: "Thank you for your trust",
    cash: "Cash",
    creditCard: "Credit Card",
    bankTransfer: "Bank Transfer",
    check: "Check",
    otherPaymentMethod: "Other Method",

    // Supplier Modal
    addNewSupplier: "Add New Supplier",
    supplierNumber: "Supplier Number",
    supplierName: "Supplier Name",
    enterSupplierName: "Enter supplier name",
    supplierPhone: "Phone Number",
    supplierEmail: "Email Address",
    supplierAddress: "Address",
    fullAddress: "Full Address",
    saveSupplier: "Save Supplier",

    // Supplier Management Modal
    suppliersManagement: "Suppliers Management",
    addNewSupplierButton: "Add New Supplier",
    supplierID: "Supplier ID",
    supplierNameHeader: "Supplier Name",
    supplierPhoneHeader: "Phone Number",
    supplierEmailHeader: "Email Address",
    supplierAddressHeader: "Address",
    noSuppliersAdded: "No suppliers added",
    deleteSupplier: "Delete",
    editSupplier: "Edit Supplier",
    confirmDeleteSupplier: "Are you sure you want to delete this supplier?",
    supplierDeletedSuccessfully: "Supplier deleted successfully",
    supplierUpdatedSuccessfully: "Supplier updated successfully",
    pleaseEnterSupplierName: "Please enter supplier name",
    enterSupplierPhone: "Enter phone number",
    enterSupplierEmail: "Enter email address",
    enterSupplierAddress: "Enter address",
    close: "Close",

    // Messages
    fillRequiredFields: "Please fill all required fields",
    usernameExists: "Username already exists",
    sellerAddedSuccess: "Seller added successfully!",
    confirmDeleteSeller: "Are you sure you want to delete this seller?",
    sellerDeletedSuccess: "Seller deleted successfully",
    sellerStatusUpdated: "Seller status updated",
    storeSettingsSaved: "Store settings saved successfully",
    expenseUpdatedSuccess: "Expense updated successfully",
    expenseAddedSuccess: "Expense added successfully",

    // Edit Product Modal
    editProduct: "Edit Product",
    productNumber: "Product Number",
    productName: "Product Name",
    enterProductName: "Enter product name",
    selectCategory: "Select category",
    categoryManagement: "Category Management",
    barcode: "Barcode",
    buyPrice: "Buy Price",
    sellPrice: "Sell Price",
    currentStock: "Current Stock",
    minStock: "Minimum Stock",
    saveChanges: "Save Changes",

    // New Product in Purchase Modal
    addNewProduct: "Add New Product",
    purchasedQuantity: "Purchased Quantity",
    addProduct: "Add Product",
    enterBuyPrice: "Enter buy price",
    enterSellPrice: "Enter sell price",
    enterInitialStock: "Enter initial stock",
    enterMinStock: "Enter minimum stock",
    initialStock: "Initial Stock",
    redirectToInventory: "Redirected to inventory management to add new product",
    addNewProductOpened: "Add new product window opened",

    // Edit Invoice Modal
    editInvoice: "Edit Invoice",
    invoiceNumber: "Invoice Number",
    customer: "Customer",
    invoiceItems: "Invoice Items",
    product: "Product",
    price: "Price",
    quantity: "Quantity",
    total: "Total",
    subtotal: "Subtotal",
    tax: "Tax",
    finalTotal: "Final Total",

    // Return Products Modal
    returnProducts: "Return Products from Invoice",
    invoiceDate: "Invoice Date",
    originalTotal: "Original Total",
    selectProductsToReturn: "Select Products to Return",
    originalQuantity: "Original Quantity",
    returnQuantity: "Return Quantity",
    returnValue: "Return Value",
    totalReturnValue: "Total Return Value",
    confirmReturn: "Confirm Return",

    // Sales Invoice Modal - Additional Keys
    saveInvoiceShortcut: "Save Invoice",
    addProductShortcut: "Add Product",
    closeWithoutSaving: "Close without saving",
    selectRegisteredCustomer: "Select registered customer",
    scanBarcodeLabel: "Scan Barcode",
    scanBarcodePlaceholder: "Scan barcode or enter manually",
    searchProductPlaceholder: "Search product (name, code, barcode)...",
    selectProduct: "Select product",
    available: "Available",
    noProductsAdded: "No products added yet",
    useBarcodeOrSelect: "Use barcode or select from list",
    invoiceItems: "Invoice Items",
    productName: "Product",
    action: "Action",
    deleteTitle: "Delete",
    finalTotal: "Final Total",
    saveInvoice: "Save Invoice",
    quantityRequiredExceedsStock: "Quantity required exceeds available stock",

    // Toast Notifications
    clearAllNotifications: "Clear all notifications",

    // Sales Invoice Popup Notifications
    pleaseAddProductsToInvoice: "Please add products to the invoice",
    productNotFoundInSystem: "Product not found",
    quantityExceedsStock: "Requested quantity exceeds available stock",
    salesInvoiceSavedSuccessfully: "Sales invoice number",
    successfullyAndStockUpdated: "saved successfully and stock updated!",
    invoiceSentToThermalPrinter: "Invoice sent to thermal printer",
    discountApplied: "Discount applied of",
    fromProfitMarginForCustomer: "from profit margin for customer",
    forWalkInCustomer: "for walk-in customer",
    forCustomer: "for customer",
    invoicePrintedAutomatically: "Invoice printed automatically",

    // Keyboard Shortcuts Notifications
    f1NewSalesInvoiceOpened: "New sales invoice opened",
    f2NewInvoiceOpened: "New invoice opened",
    f3ProductAdded: "Product added",
    f3ProductAddedToPurchase: "Product added to purchases",
    f3OnlyAvailableInInvoice: "Available only in invoice window",
    f6NewProductOpened: "New product window opened",
    f6NavigatedToInventory: "Navigated to inventory management and opened new product window",
    f7NewPurchaseInvoiceOpened: "New purchase invoice opened",
    f7NavigatedToPurchases: "Navigated to purchase management and opened new purchase invoice",

    // Product Management Notifications
    productAddedToInvoice: "Added",
    toInvoice: "to invoice",
    productUpdatedSuccessfully: "Product updated",
    successfully: "successfully",
    productAddedWithScannedBarcode: "Product added",
    withScannedBarcode: "with scanned barcode:",
    productAddedWithAutoBarcode: "Product added",
    withAutoBarcode: "with automatic barcode:",
    confirmDeleteProduct: "Are you sure you want to delete the product",
    productDeletedSuccessfully: "Product deleted",

    // Supplier Management Notifications
    pleaseEnterSupplierName: "Please enter supplier name",
    supplierAddedSuccessfully: "Supplier added successfully",
    confirmDeleteSupplier: "Are you sure you want to delete this supplier?",
    supplierDeletedSuccessfully: "Supplier deleted successfully",

    // System Control Notifications
    soundEnabled: "Sounds enabled",
    soundDisabled: "Sounds disabled",
    keyboardShortcutsInfo: "Shortcuts active: F1 sales invoice, F2 new invoice, F3 add, F6 new product, F7 purchase invoice, Enter save, ESC close",
    keyboardShortcutsEnabled: "Keyboard shortcuts enabled",
    keyboardShortcutsDisabled: "Keyboard shortcuts disabled",
    enableKeyboardShortcuts: "Enable keyboard shortcuts",
    disableKeyboardShortcuts: "Disable keyboard shortcuts",
    printerEnabled: "Printer enabled",
    printerDisabled: "Printer disabled",
    notificationsEnabled: "Notifications enabled",
    notificationsDisabled: "Notifications disabled",
    enableNotifications: "Enable notifications",
    disableNotifications: "Disable notifications",
    addProduct: "Add product",
    scrollToTop: "Scroll to top",
    saveInvoice: "Save invoice",

    // Credit Limit Validation
    creditLimitExceeded: "Credit limit exceeded",
    currentBalance: "Current balance",
    invoiceAmount: "Invoice amount",
    newBalance: "New balance",
    exceededAmount: "Exceeded amount",

    // Bulk Selection
    selectAll: "Select All",
    deselectAll: "Deselect All",
    deleteSelected: "Delete Selected",
    pleaseSelectItems: "Please select items to delete",
    confirmDeleteSelected: "Are you sure you want to delete the selected items?",
    selectedItemsDeleted: "Selected items deleted",
    itemsSelected: "items selected",

    // Invoice Management Notifications
    invoiceDeletedAndStockRestored: "Invoice deleted",
    andStockRestored: "and products returned to stock",
    notAllowedManagerOnly: "Not allowed - Only manager can modify stock",
    stockQuantityUpdated: "Stock quantity updated successfully",
    productRemovedFromInvoice: "Removed",
    fromInvoice: "from invoice",
    productRemovedFromPurchase: "Product removed from invoice",
    confirmDeleteInvoice: "Are you sure you want to delete invoice number",
    allProductsWillBeRestored: "All products will be returned to stock.",
    confirmDeletePurchaseInvoice: "Are you sure you want to delete purchase invoice",
    purchaseInvoiceDeletedAndStockAdjusted: "Purchase invoice deleted and stock readjusted",
    invoiceUpdatedAndStockAdjusted: "Invoice updated",
    andStockAdjusted: "and stock updated",
    confirmDeleteAllData: "Are you sure you want to delete all data? This action cannot be undone!",
    allInventoryDataDeleted: "All inventory data deleted",
    reportSentToPrinter: "Report sent to printer",

    // Purchase Management Notifications
    pleaseSelectProduct: "Please select a product",
    pleaseEnterValidQuantity: "Please enter a valid quantity",
    pleaseEnterValidPrice: "Please enter a valid price",
    pleaseSelectSupplier: "Please select a supplier",
    purchaseInvoiceOpenedForEdit: "Purchase invoice opened for editing",
    notAllowedManagerOnlyPurchase: "Not allowed - Only manager can edit purchase invoices",
    notAllowedManagerOnlyDeletePurchase: "Not allowed - Only manager can delete purchase invoices",
    generalSupplier: "General supplier",

    // Category Management Notifications
    pleaseEnterCategoryName: "Please enter category name",
    categoryAlreadyExists: "Category already exists",
    categoryAddedSuccessfully: "Category added",

    // Customer Management Notifications
    pleaseEnterCustomerName: "Please enter customer name",
    customerAddedSuccessfully: "Customer added successfully",
    customerDeletedSuccessfully: "Customer deleted successfully",
    customerDataUpdatedSuccessfully: "Customer data updated successfully",

    // Product Management Notifications
    pleaseEnterProductName: "Please enter product name",


    scanBarcodeToAddProduct: "Scan barcode - press Enter to open invoice",
    openInvoiceWithProduct: "Open invoice with product",
    openInvoice: "Open Invoice",
    dashboardBarcodeHelp: "Scan barcode to display information, press Enter or open invoice button to add product to invoice",
    scanProductToDisplay: "Scan product to display information",
    productInfo: "Product Information",

    // NEW: Barcode Scanning Messages
    productOutOfStock: "Product out of stock",
    quantityExceedsStock: "Quantity exceeds available stock",
    available: "available",
    quantityIncreased: "Quantity increased",
    productAdded: "Product added",
    productNotFound: "Product not found with this barcode",
    barcodeMinLength: "Barcode too short - must be at least 3 characters",
    noInvoiceBeingEdited: "No invoice is being edited",

    // Barcode & Product Search Notifications
    productAddedToInvoiceAutomatically: "Added",
    toInvoiceAutomatically: "to invoice automatically",
    productFoundWithBarcode: "Found",
    noProductFoundWithBarcode: "No product found with this barcode",
    autoBarcodeGenerated: "Auto barcode generated",
    barcodeCleared: "Barcode cleared",
    barcodeAlreadyExists: "Barcode already exists in another product",

    // Login & Authentication Notifications
    welcomeUser: "Welcome",
    accountInactiveContactManager: "Your account is inactive, please contact the manager",
    invalidUsernameOrPassword: "Invalid username or password",

    // Invoice Details Modal
    invoiceDetails: "Invoice Details",
    salesInvoice: "Sales Invoice",
    invoiceInfo: "Invoice Information",
    customerInfo: "Customer Information",
    customerName: "Customer Name",
    creationTime: "Creation Time",

    // Thermal Printing
    invoiceNumberLabel: "Invoice No:",
    dateLabel: "Date:",
    timeLabel: "Time:",
    customerLabel: "Customer:",
    paymentMethodLabel: "Payment Method:",
    productsLabel: "Products",
    subtotalLabel: "Subtotal:",
    discountLabel: "Discount:",
    taxLabel: "Tax",
    finalTotalLabel: "Final Total:",
    thankYouMessage: "Thank you for your visit",
    printedAtLabel: "Printed at:",
    salesInvoiceTitle: "Sales Invoice",
    accountingSystem: "Accounting System",
    developedBy: "Developed by",

    // Enhanced Thermal Printing
    thermalInvoice: "Thermal Invoice",
    thermalPrintError: "Thermal printing error",
    printError: "Printing error",
    popupBlocked: "Popup blocked - Please allow popups",
    directPrintingEnabled: "Direct printing enabled",
    thermalPrinterDetected: "Thermal printer detected",
    thermalPrinterNotDetected: "Thermal printer not detected",
    printingDirectly: "Printing directly...",
    printingSentToThermal: "Print sent to thermal printer",
    storeNumber: "Store Number",
    qtyColumn: "QNT",

    // Invoice Details Modal - Complete
    invoiceNumberColon: "Invoice Number:",
    dateColon: "Date:",
    creationTimeColon: "Creation Time:",
    customerInfoTitle: "Customer Information",
    customerNameColon: "Customer Name:",
    paymentMethodColon: "Payment Method:",
    productsTitle: "Products",
    productColumn: "Product",
    quantityColumn: "Quantity",
    priceColumn: "Price",
    totalColumn: "Total",
    noProductsInInvoice: "No products in this invoice",
    subtotalColon: "Subtotal:",
    taxColon: "Tax",
    discountColon: "Discount:",
    finalTotalColon: "Final Total:",
    normalPrintButton: "Normal Print",
    thermalPrintButton: "Thermal Print",
    closeButton: "Close",
    backToTop: "Back to Top",

    // Return Process Notifications
    pleaseSelectAtLeastOneProduct: "Please select a return quantity for at least one product",
    productsReturnedSuccessfully: "Products returned successfully",
    andStockUpdated: "and stock updated",

    // Product Modal - Additional Keys
    editExistingProduct: "Edit Existing Product",
    scanBarcodeOrEnter: "Scan barcode or enter manually - will be auto-generated if left empty",
    generateAutoBarcode: "Generate Auto Barcode",
    clearBarcode: "Clear Barcode",
    barcodeHelp: "You can scan the barcode using a barcode reader or enter it manually. If the barcode exists in inventory, product information will be displayed for editing. If left empty, a barcode will be auto-generated when saving.",
    currentBarcode: "📷 Current Barcode",
    barcodeWillShow: "Barcode will be displayed here",
    currentQuantity: "Current Quantity",
    saveProduct: "Save Product",
    manageCategories: "Manage Categories",
    productNameAlreadyExists: "Product name already exists",
    pleaseChooseDifferentName: "Please choose a different name",
    productExistsWithBarcode: "A product exists with this barcode",
    checkBeforeSaving: "Check before saving",
    barcodeAlreadyExists: "Barcode already exists",
    usedByProduct: "used by product",
    customerNameAlreadyExists: "Customer name already exists",
    supplierNameAlreadyExists: "Supplier name already exists",
    sellerNameAlreadyExists: "Seller name already exists",
    phoneNumberAlreadyExists: "Phone number already exists",
    usedByCustomer: "used by customer",
    usedBySupplier: "used by supplier",
    usedBySeller: "used by seller",
    productFoundMessage: "Product found",
    canEditAndSave: "you can edit the information and save",
    barcodeScannedSuccess: "Barcode scanned successfully - new barcode",
    barcodeAvailable: "Barcode available for use - new product",

    // Category Management Modal
    addNewCategory: "Add New Category",
    newCategoryName: "New category name",
    existingCategories: "Existing Categories",
    edit: "Edit",

    // Toast Messages for Products
    productFoundMessage: "Product found",
    canEditAndSave: "You can edit the information and save",
    barcodeScannedSuccess: "Barcode scanned successfully - new barcode",
    barcodeAvailable: "Barcode available for use - new product",
    autoBarcodeGenerated: "Auto barcode generated",
    barcodeCleared: "Barcode cleared",

    // Messages
    loginSuccess: "Login successful",
    loginError: "Username or password error",
    invoiceSaved: "Invoice saved successfully",
    customerAdded: "Customer added successfully",
    customerDeleted: "Customer deleted successfully",
    productAdded: "Product added successfully",
    settingsSaved: "Settings saved successfully",
    pleaseSelectCustomer: "Please select a customer",
    pleaseAddProducts: "Please add products to the invoice",
    productNotFound: "No product found with this barcode",

    // Activation Dialog
    activationTitle: "Program Activation",
    activationDescription: "Please enter your activation code to activate the program for lifetime",
    activationCodeLabel: "Activation Code:",
    activationCodePlaceholder: "ICAL-2025-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX",
    activateProgram: "Activate Program",
    activating: "Activating...",
    resetActivation: "Reset Activation (Testing)",
    resetActivationTooltip: "Reset activation for testing",
    deviceInfo: "Device Information:",
    deviceId: "Device ID:",
    deviceNote: "Activation will be permanently linked to this device",
    getActivationCode: "To get activation code:",
    phone: "Phone:",
    email: "Email:",
    website: "Website:",

    // Activation Error Messages
    pleaseEnterActivationCode: "Please enter the activation code",
    unexpectedActivationError: "An unexpected error occurred during activation",
    programAlreadyActivated: "Program is already activated on this device\n\n💡 For testing: type \"reset\" or \"test\" to show reset option",
    confirmResetActivation: "Are you sure you want to reset the activation?\n\nThe current activation will be deleted and you can test a new code.",
    resetActivationSuccess: "✅ Activation reset successful!\n\nYou can now test a new activation code.",
    invalidActivationCodeFormat: "Activation code format is incorrect",

    // Reports and Statistics
    reportsAndStatistics: "Reports and Statistics",
    comprehensiveReports: "Comprehensive reports for all financial and commercial operations - Manager only",

    // Financial Reports
    financialReports: "Financial Reports",
    financialReportsDesc: "Sales, purchase and profit reports",
    salesReport: "Sales Report",
    salesReportDesc: "Total sales and invoices",
    purchaseReport: "Purchase Report",
    purchaseReportDesc: "Total purchases and suppliers",
    profitReport: "Profit Report",
    profitReportDesc: "Profit and loss analysis",

    // Inventory Reports
    inventoryReports: "Inventory Reports",
    inventoryReportsDesc: "Stock status and products",
    generalInventoryReport: "General Inventory Report",
    generalInventoryReportDesc: "All products and stock status",
    lowStockReport: "Low Stock Report",
    lowStockReportDesc: "Products requiring restocking",
    outOfStockReport: "Out of Stock Report",
    outOfStockReportDesc: "Unavailable products",

    // Customer Reports
    customerReports: "Customer Reports",
    customerReportsDesc: "Customer and sales analysis",
    customersReport: "Customers Report",
    customersReportDesc: "Customer list and their data",
    debtorsReport: "Debtors Report",
    debtorsReportDesc: "Debtor customers and receivables",
    topCustomersReport: "Top Customers",
    topCustomersReportDesc: "Customers with most purchases",
    customerAnalysisReport: "Customer Analysis",
    customerAnalysisReportDesc: "Detailed customer statistics",

    // Performance Reports
    performanceReports: "Performance Reports",
    performanceReportsDesc: "Performance indicators and statistics",
    dailyReport: "Daily Report",
    dailyReportDesc: "Daily operations summary",
    weeklyReport: "Weekly Report",
    weeklyReportDesc: "Weekly operations summary",
    monthlyReport: "Monthly Report",
    monthlyReportDesc: "Monthly operations summary",
    yearlyReport: "Annual Report",
    yearlyReportDesc: "Annual operations summary",
    kpiReport: "Performance Indicators",
    kpiReportDesc: "KPIs and performance metrics",

    // Report Statistics Labels
    totalDebts: "Total Debts",
    activeCustomer: "Active Customer",
    today: "Today",
    currentWeek: "Current Week",
    currentMonth: "Current Month",
    currentYear: "Current Year",
    analysis: "Analysis",

    // Additional Reports
    inventoryValueReport: "Inventory Value Report",
    inventoryValueReportDesc: "Total inventory value",
    categoriesReport: "Categories Report",
    categoriesReportDesc: "Product analysis by categories",
    advancedReports: "Advanced Reports",
    advancedReportsDesc: "Detailed reports with advanced filters",
    businessManagement: "Integrated Business Management",
    quickSummary: "Quick Summary",
    totalSales: "Total Sales",
    totalPurchases: "Total Purchases",
    netProfit: "Net Profit",
    inventoryValue: "Inventory Value",
    totalValue: "Total Value",
    category: "Category",

    // Advanced Reports Form Labels
    reportType: "Report Type:",
    date: "Date:",
    month: "Month:",
    daily: "Daily",
    monthly: "Monthly",
    print: "Print",

    // Cash Flow Report
    cashFlowReport: "Cash Flow Report",
    cashFlowReportDesc: "Incoming and outgoing cash movement",
    cashMovement: "Incoming and outgoing cash movement",
    cash: "Cash",
    inCash: "Cash",
    accountingSystem: "Accounting System",
    reportDate: "Report Date",
    cashManagement: "Cash Management",

    // Common Report Labels
    cashSales: "Cash Sales",
    creditSales: "Credit Sales",
    cashPurchases: "Cash Purchases",
    creditPurchases: "Credit Purchases",
    netCashFlow: "Net Cash Flow",
    cashInflow: "Cash Inflow",
    cashOutflow: "Cash Outflow",
    cashSurplus: "Cash Surplus",
    cashDeficit: "Cash Deficit",
    receivables: "Accounts Receivable",
    payables: "Accounts Payable",
    invoiceNumber: "Invoice Number",
    customerName: "Customer Name",
    paymentMethod: "Payment Method",
    subtotal: "Subtotal",
    tax: "Tax",
    discount: "Discount",
    finalAmount: "Final Amount",
    status: "Status",
    paid: "Paid",
    debt: "Credit",
    walkInCustomer: "Walk-in Customer",
    salesManagement: "Sales Management",
    businessManagement: "Integrated Business Management",
    invoiceCount: "Invoice Count",
    averageInvoice: "Average Invoice",
    purchaseManagement: "Purchase Management",

    // Additional Report Labels
    totalPurchases: "Total Purchases",
    supplierName: "Supplier Name",
    grossProfit: "Gross Profit",
    netProfit: "Net Profit",
    operatingExpenses: "Operating Expenses",
    profitMargin: "Profit Margin",
    costOfGoodsSold: "Cost of Goods Sold",
    profitAnalysis: "Profit and Loss Analysis",
    inventoryManagement: "Inventory Management",
    stockValue: "Stock Value",
    lowStock: "Low Stock",
    categoryAnalysis: "Category Analysis",
    customerAnalysis: "Customer Analysis",
    topCustomers: "Top Customers",
    debtorsAnalysis: "Debtors Analysis",
    performanceAnalysis: "Performance Analysis",
    dailyOperations: "Daily Operations",
    monthlyOperations: "Monthly Operations",
    yearlyOperations: "Yearly Operations",
    kpiAnalysis: "KPI Analysis",

    // Access Control
    accessRestricted: "Access Restricted",
    reportsManagerOnly: "Reports and statistics are available for manager only",
    loginAsManager: "Please login with a manager account to access this page",
    backToDashboard: "Back to Dashboard",

    // Login
    login: "Login",
    username: "Username",
    password: "Password",
    welcomeBack: "Welcome Back",
    enterCredentials: "Enter your credentials to access the system",

    // Additional Report Translations
    salesReport: "Sales Report",
    purchaseReport: "Purchase Report",
    profitReport: "Profit Report",
    profitLossReport: "Profit & Loss Report",
    inventoryValueReport: "Inventory Value Report",
    financialAnalysis: "Financial Analysis",
    inventoryValuation: "Inventory Valuation",
    supplier: "Supplier",
    unknownSupplier: "Unknown Supplier",
    totalSales: "Total Sales",
    totalRevenueFromSales: "Total Revenue from Sales",
    costOfProductsSold: "Cost of Products Sold",
    salariesRentOtherExpenses: "Salaries, Rent & Other Expenses",
    netProfitLoss: "Net Profit/Loss",
    netLoss: "Net Loss",
    net: "net",
    profitPercentageFromSales: "Profit Percentage from Sales",
    credit: "Credit",
    totalInventoryValue: "Total Inventory Value",
    productCount: "Product Count",
    totalQuantities: "Total Quantities",
    averageProductValue: "Average Product Value",
    productName: "Product Name",
    quantity: "Quantity",
    buyPrice: "Buy Price",

    // Customer Report Translations
    customersReport: "Customers Report",
    customerManagement: "Customer Management",
    activeCustomers: "Active Customers",
    newCustomersThisMonth: "New Customers This Month",
    averagePurchasesPerCustomer: "Average Purchases per Customer",
    customerNumber: "Customer Number",
    phoneNumber: "Phone Number",
    registrationDate: "Registration Date",
    purchaseCount: "Purchase Count",
    notSpecified: "Not Specified",
    active: "Active",
    inactive: "Inactive",

    // Additional Report Translations
    debtorsReport: "Debtors Report",
    bestCustomers: "Best Customers",
    customerAnalysis: "Customer Analysis",
    dailyReport: "Daily Report",
    advancedReports: "Advanced Reports",
    monthlyReport: "Monthly Report",
    annualReport: "Annual Report",
    performanceIndicators: "Performance Indicators",
    categoryReport: "Category Report",
    generalStockReport: "General Stock Report",
    lowStockReport: "Low Stock Report",

    // Footer Translations
    reportGeneratedBy: "This report was automatically generated by the accounting system designed by iCode DZ",
    allRightsReserved: "© 2025 iDesign DZ +213 551 93 05 89 - All rights reserved",

    // New Report System Translations
    newSystem: "New System",
    accurateCalculations: "100% Accurate Calculations",
    costOfGoodsSold: "Cost of Goods Sold",
    grossProfit: "Gross Profit",
    beforeExpenses: "Before Expenses",
    operatingExpenses: "Operating Expenses",
    dailyNetProfit: "Daily Net Profit",
    profit: "Profit",
    loss: "Loss",
    fromSales: "From Sales",
    processedItems: "Processed Items",
    from: "From",
    item: "Item",
    expense: "Expense",
    dailyReportOpenedWithWarnings: "📊 Daily report opened with warnings - Please check data",
    dailyReportOpenedSuccessfully: "📊 Daily report opened successfully - 100% accurate calculations",
    annualOperationsSummary: "Annual Operations Summary",
    improvedNewSystem: "Improved New System",
    noErrors: "No Errors",
    annualNetProfit: "Annual Net Profit",
    margin: "Margin",
    monthlyPerformance: "Monthly Performance",
    sales: "Sales",
    cost: "Cost",
    annualReportOpenedSuccessfully: "📊 Annual report opened successfully - 100% accurate calculations",
    monthlyOperationsSummary: "Monthly Operations Summary",
    monthlyNetProfit: "Monthly Net Profit",
    dailyPerformanceForMonth: "Daily Performance for Month",
    monthlyReportOpenedSuccessfully: "📅 Monthly report opened successfully - 100% accurate calculations",
    invalidDataReload: "❌ Invalid data, please reload the page",
    reportValidationFailed: "⚠️ Report calculation validation failed",
    missingProductsWarning: "⚠️ Warning: missing products",
    dailyReportGenerated: "📊 Daily report generated",
    monthlyReportGenerated: "📊 Monthly report generated",
    generateReportFirst: "⚠️ Please generate the report first",
    reportOpenedForPrint: "🖨️ Report opened for printing",

    // Report Toast Messages
    salesReportOpened: "📈 Sales report opened for printing",
    purchaseReportOpened: "📉 Purchase report opened for printing",
    profitReportOpened: "💎 Profit report opened for printing",
    inventoryValueReportOpened: "💰 Inventory value report opened for printing",
    cashFlowReportOpened: "💸 Cash flow report opened for printing",
    categoriesReportOpened: "🏷️ Categories report opened for printing",
    customersReportOpened: "👤 Customers report opened for printing",
    debtorsReportOpened: "💳 Debtors report opened for printing",
    topCustomersReportOpened: "🌟 Top customers report opened for printing",
    customerAnalysisReportOpened: "📊 Advanced customer analysis opened for printing",
    kpiReportOpened: "🎯 KPI report opened for printing",

    // Advanced Reports Content Translations
    creationDate: "Creation Date",
    soldProductsDetails: "Sold Products Details",
    totalCost: "Total Cost",

    // KPI Report Translations
    kpiDashboard: "KPI Dashboard",
    performanceOverview: "Performance Overview",
    allPeriods: "All Periods",
    margin: "margin",
    growth: "growth",
    financialPerformance: "Financial Performance",
    customerMetrics: "Customer Metrics",
    inventoryMetrics: "Inventory Metrics",
    operationalMetrics: "Operational Metrics",
    uniqueCustomers: "Unique Customers",
    averageOrderValue: "Average Order Value",
    customersWithDebt: "Customers with Debt",
    inventoryTurnover: "Inventory Turnover",
    averageDailySales: "Average Daily Sales",
    salesVelocity: "Sales Velocity",
    cashRatio: "Cash Ratio",
    monthlyGrowthRate: "Monthly Growth Rate",
    excellent: "Excellent",
    good: "Good",
    warning: "Warning",

    // Debtors Report Translations
    debtorsAndReceivables: "Debtors and Receivables",
    totalOutstandingAmount: "Total Outstanding Amount",
    followUpRequired: "Follow-up required for collection of these amounts",
    debtorCustomers: "Debtor Customers",
    outstandingInvoices: "Outstanding Invoices",
    averageDebtPerCustomer: "Average Debt per Customer",
    highestOutstandingAmount: "Highest Outstanding Amount",
    customerName: "Customer Name",
    invoiceCount: "Invoice Count",
    totalOutstanding: "Total Outstanding",
    oldestInvoice: "Oldest Invoice",
    newestInvoice: "Newest Invoice",
    priorityLevel: "Priority Level",
    high: "High",
    medium: "Medium",
    low: "Low",

    // Top Customers Report Translations
    topCustomers: "Top Customers",
    topCustomersAnalysis: "Top Performing Customers Analysis",
    topCustomer: "Top Customer",
    totalSalesTopTen: "Total Sales of Top 10",
    averagePurchasesTopCustomer: "Average Purchases of Top Customer",
    totalInvoicesCount: "Total Invoice Count",
    percentageOfTotalSales: "Percentage of Total Sales",
    ranking: "Ranking",
    totalPurchases: "Total Purchases",
    averageInvoice: "Average Invoice",
    firstPurchase: "First Purchase",
    lastPurchase: "Last Purchase",
    membershipPeriod: "Membership Period",
    days: "days",

    // Customer Analysis Report Translations
    advancedCustomerAnalysis: "Advanced Customer Analysis",
    businessIntelligence: "Business Intelligence",
    generalCustomerStats: "General Customer Statistics",
    newCustomersThisMonth: "New Customers This Month",
    customersWithPurchases: "Customers with Purchases",
    debtorCustomersCount: "Debtor Customers",
    conversionRate: "Conversion Rate",
    purchaseFrequencyAnalysis: "Purchase Frequency Analysis",
    oneTimeBuyers: "One-time Buyers",
    occasionalBuyers: "Occasional Buyers",
    regularBuyers: "Regular Buyers",
    frequentBuyers: "Frequent Buyers",
    insightsAndRecommendations: "Insights and Recommendations",
    retentionRate: "Retention Rate",
    customersReturnToBuy: "of customers return to buy again",
    growthOpportunity: "Growth Opportunity",
    oneTimePurchaseOpportunity: "customer purchased only once - opportunity for retargeting campaigns",
    loyalCustomers: "Loyal Customers",
    regularPurchasers: "customer purchase regularly - should be nurtured with special offers",
    customerGrowth: "Customer Growth",
    newCustomersThisMonthInsight: "new customer this month",
    positiveGrowth: "- positive growth",
    needsMarketingImprovement: "- needs marketing improvement",
    debtManagement: "Debt Management",
    debtFollowUpNeeded: "customer has debts - needs follow-up for collection",

    // Category Report Translations
    categoriesReport: "Categories Report",
    categoryAnalysis: "Category Analysis",
    productCount: "Product Count",
    totalQuantities: "Total Quantities",
    totalValue: "Total Value",
    inventoryPercentage: "Inventory Percentage",

    // Excel Export Translations
    productCode: "Product Code",
    productName: "Product Name",
    barcode: "Barcode",
    buyPrice: "Buy Price",
    sellPrice: "Sell Price",
    availableQuantity: "Available Quantity",
    minStock: "Minimum Stock",
    currency: "DZD",
    status: "Status",
    lowStock: "Low",
    outOfStock: "Out of Stock",
    highStock: "High",
    normalStock: "Normal",
    summary: "Summary",
    totalProducts: "Total Products",
    lowStockProducts: "Low Stock Products",
    outOfStockProducts: "Out of Stock Products",
    inventoryReport: "Inventory Report",

    // Additional KPI Translations
    profitFromSales: "Profit percentage from sales",
    cashVsCredit: "Cash vs credit sales",
    todaySales: "Today's Sales",
    monthlySales: "Monthly Sales",
    netProfit: "Net Profit",
    profitMargin: "Profit Margin",
    totalCustomers: "Total Customers",
    activeCustomers: "Active Customers",
    inventoryValue: "Inventory Value",
    lowStockItems: "Low Stock Items",
    outOfStockItems: "Out of Stock Items",
    monthlyGrowthRate: "Monthly Growth Rate",

    // Advanced Report Display Translations
    topProducts: "Top Selling Products",
    quantitySold: "Quantity Sold",
    totalRevenue: "Total Revenue",
    profit: "Profit",

    // Additional KPI Detail Translations
    uniqueCustomersDesc: "Number of unique customers",
    customersWithDebtDesc: "Number of customers with debts",
    totalDebtDesc: "Amounts owed by customers",
    averageOrdersPerCustomer: "Average orders per customer",
    purchaseFrequency: "Purchase frequency",
    largeCustomerBase: "Large customer base",
    mediumCustomerBase: "Medium customer base",
    smallCustomerBase: "Small customer base",
    lowRatio: "Low ratio",
    mediumRatio: "Medium ratio",
    highRatio: "High ratio",
    highLoyalty: "High loyalty",
    mediumLoyalty: "Medium loyalty",
    lowLoyalty: "Low loyalty",
    totalGoodsValue: "Total goods value",
    inventoryInvestment: "Inventory investment",
    needsRestocking: "Products needing restocking",
    unavailableProducts: "Unavailable products",
    stockSalesSpeed: "Stock sales speed",
    goodStock: "Good stock",
    needsMonitoring: "Needs monitoring",
    urgentRestocking: "Urgent restocking needed",
    none: "None",
    few: "Few",
    many: "Many",
    fastTurnover: "Fast turnover",
    mediumTurnover: "Medium turnover",
    slowTurnover: "Slow turnover",
    excellentProfitMargin: "Excellent profit margin indicating efficient pricing",
    improveProfitMargin: "Can improve profit margin by reviewing costs and prices",
    customerManagement: "Customer Management",
    goodDebtManagement: "Good debt and credit management",
    improveCreditPolicies: "Needs improvement in credit policies and collection follow-up",
    inventoryManagement: "Inventory Management",
    appropriateStockLevel: "Appropriate stock level",
    improveInventoryPlanning: "Needs improvement in inventory planning and restocking",
    growth: "Growth",
    thisMonth: "this month",
    needsGrowthStrategies: "Needs strategies to stimulate growth",
    cashFlow: "Cash Flow",
    healthyCashFlow: "Healthy cash flow with good cash sales ratio",
    improveCashFlow: "Needs improvement in cash flow and reduction of credit sales",

    // Month Names (English)
    month1: "January",
    month2: "February",
    month3: "March",
    month4: "April",
    month5: "May",
    month6: "June",
    month7: "July",
    month8: "August",
    month9: "September",
    month10: "October",
    month11: "November",
    month12: "December",

    // Control Panel Tooltips
    disableSounds: "Disable sounds",
    enableSounds: "Enable sounds",
    keyboardShortcutsAlwaysActive: "Keyboard shortcuts always active",
    disablePrinter: "Disable printer",
    enablePrinter: "Enable printer",

    // Dashboard Action Messages
    newPurchaseInvoiceClicked: "New purchase invoice clicked",
    purchaseReportClicked: "Navigated to purchases page",
    purchaseStatisticsClicked: "Navigated to reports and statistics page",
    willOpenNewPurchaseInvoiceWindow: "📄 Will open new purchase invoice creation window",
    willOpenDetailedPurchaseReport: "📊 Will open detailed purchase report",
    willOpenDetailedPurchaseStatistics: "📈 Will open detailed purchase statistics",

    // Print Functions
    invoiceNumberLabel: "Invoice No:",
    dateLabel: "Date:",
    customerLabel: "Customer:",
    paymentMethodLabel: "Payment Method:",
    productsLabel: "Products",
    subtotalLabel: "Subtotal:",
    discountLabel: "Discount:",
    taxLabel: "Tax",
    finalTotalLabel: "Final Total:",
    thankYouMessage: "Thank you for your visit",
    printedAtLabel: "Printed at:",
    salesInvoiceTitle: "Sales Invoice",
    invoiceInfo: "Invoice Information",
    customerInfo: "Customer Information",
    customerName: "Customer Name",
    creationTime: "Creation Time",
    paymentInfo: "Payment Information",
    invoiceStatus: "Invoice Status",
    paidStatus: "Paid",
    debtStatus: "Credit",
    thankYouForDealingWithUs: "Thank you for dealing with us",
    allRightsReserved: "All rights reserved",
    printWindowOpened: "Print window opened",

    // Edit Invoice Modal
    editInvoiceTitle: "Edit Invoice",
    invoiceItems: "Invoice Items",
    actions: "Actions",
    saveChanges: "Save Changes",
    cancel: "Cancel",
    insufficientStockForProduct: "Insufficient stock for product",
    invoiceUpdatedAndStockAdjusted: "Invoice updated",
    andStockAdjusted: "and stock adjusted",
    notAllowedManagerOnlyEditInvoices: "Not allowed - Only manager can edit invoices",

    // Credit Invoice Validation
    cannotSaveCreditForWalkInCustomer: "Cannot save credit invoice for walk-in customer",
    pleaseSelectRegisteredCustomerForCredit: "Please select a registered customer for credit payment",

    // Sales Report Print
    additionalStatistics: "Additional Statistics",
    totalTaxes: "Total Taxes",
    totalDiscounts: "Total Discounts",
    creditInvoicesCount: "Credit Invoices",
    creditInvoicesValue: "Credit Value",
    professionalSalesReportOpened: "Professional sales report opened for printing",

    // Additional Sales Report Keys
    salesSummary: "Sales Summary",
    salesInvoiceDetails: "Sales Invoice Details",
    reportDate: "Report Date",
    generationTime: "Generation Time",
    noSalesInvoicesToDisplay: "No sales invoices to display",
    finalTotal: "Final Total",
    lastInvoice: "Last Invoice",

    // Dashboard LCD Display
    scanBarcodeToOpenInvoice: "Scan barcode and press Enter to open invoice",
    scannerActive: "Scanner Active",

    // Modal Shortcuts
    saveInvoiceShortcut: "Save Invoice",
    addProductShortcut: "Add Product",
    closeWithoutSaving: "Close Without Saving",
    savePurchaseInvoice: "Save Purchase Invoice"
  }
};

export const getTranslation = (key, language = 'ar') => {
  return translations[language]?.[key] || translations.ar[key] || key;
};
